{"name": "ts-node-dev", "version": "1.1.8", "description": "Compiles your TS app and restarts when files are modified.", "keywords": ["restart", "reload", "supervisor", "monitor", "watch"], "repository": {"type": "git", "url": "http://github.com/whitecolor/ts-node-dev.git"}, "license": "MIT", "bin": {"ts-node-dev": "lib/bin.js", "tsnd": "lib/bin.js"}, "main": "lib", "files": ["icons", "lib"], "prettier": {"singleQuote": true, "semi": false}, "engines": {"node": ">=0.8.0"}, "scripts": {"ts-node-dev": "node ./lib/bin", "build": "tsc -p tsconfig.build.json", "release": "np", "test": "yarn build && ts-node -T node_modules/mocha/bin/mocha test/*.test.ts", "test-dev": "yarn ts-node-dev -T --respawn --deps --watch lib node_modules/mocha/bin/mocha test/*.test.ts --output", "test-docker": "docker-compose up", "ci": "yarn test", "ci-local": "docker run --name travis-debug -dit quay.io/travisci/ci-nodejs", "manual": "yarn ts-node test/manual/run.ts"}, "dependencies": {"chokidar": "^3.5.1", "dynamic-dedupe": "^0.3.0", "minimist": "^1.2.5", "mkdirp": "^1.0.4", "resolve": "^1.0.0", "rimraf": "^2.6.1", "source-map-support": "^0.5.12", "tree-kill": "^1.2.2", "ts-node": "^9.0.0", "tsconfig": "^7.0.0"}, "devDependencies": {"@types/chai": "^4.2.12", "@types/chokidar": "^2.1.3", "@types/fs-extra": "^9.0.1", "@types/minimist": "^1.2.0", "@types/mkdirp": "^1.0.1", "@types/mocha": "github:whitecolor/mocha-types", "@types/node": "^14.6.0", "@types/rimraf": "^3.0.0", "@types/tape": "^4.13.0", "@types/touch": "^3.1.1", "@types/ts-nameof": "^4.2.1", "@whitecolor/eslint-config": "^1.0.0", "chai": "^4.2.0", "chalk": "^4.1.0", "coffee-script": "^1.8.0", "eslint": "^7.7.0", "esm": "^3.2.22", "fs-extra": "^9.0.1", "mocha": "^8.1.1", "np": "^6.5.0", "tap": "^5.2.0", "tape": "^5.0.1", "touch": "^1.0.0", "ts-nameof": "^5.0.0", "tsconfig-paths": "^3.3.1", "ttypescript": "^1.5.10", "typescript": "^3.9.5"}, "peerDependencies": {"node-notifier": "*", "typescript": "*"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "np": {"yarn": false}, "publishConfig": {"registry": "https://registry.npmjs.org"}}