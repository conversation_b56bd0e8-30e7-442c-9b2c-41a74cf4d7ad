/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */


"use strict";

// src/compiler/corePublic.ts
var versionMajorMinor = "5.9";
var version = "5.9.2";

// src/compiler/core.ts
var emptyArray = [];
var emptyMap = /* @__PURE__ */ new Map();
function length(array) {
  return array !== void 0 ? array.length : 0;
}
function forEach(array, callback) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const result = callback(array[i], i);
      if (result) {
        return result;
      }
    }
  }
  return void 0;
}
function firstDefined(array, callback) {
  if (array === void 0) {
    return void 0;
  }
  for (let i = 0; i < array.length; i++) {
    const result = callback(array[i], i);
    if (result !== void 0) {
      return result;
    }
  }
  return void 0;
}
function firstDefinedIterator(iter, callback) {
  for (const value of iter) {
    const result = callback(value);
    if (result !== void 0) {
      return result;
    }
  }
  return void 0;
}
function reduceLeftIterator(iterator, f, initial) {
  let result = initial;
  if (iterator) {
    let pos = 0;
    for (const value of iterator) {
      result = f(result, value, pos);
      pos++;
    }
  }
  return result;
}
function zipWith(arrayA, arrayB, callback) {
  const result = [];
  Debug.assertEqual(arrayA.length, arrayB.length);
  for (let i = 0; i < arrayA.length; i++) {
    result.push(callback(arrayA[i], arrayB[i], i));
  }
  return result;
}
function every(array, callback) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      if (!callback(array[i], i)) {
        return false;
      }
    }
  }
  return true;
}
function find(array, predicate, startIndex) {
  if (array === void 0) return void 0;
  for (let i = startIndex ?? 0; i < array.length; i++) {
    const value = array[i];
    if (predicate(value, i)) {
      return value;
    }
  }
  return void 0;
}
function findLast(array, predicate, startIndex) {
  if (array === void 0) return void 0;
  for (let i = startIndex ?? array.length - 1; i >= 0; i--) {
    const value = array[i];
    if (predicate(value, i)) {
      return value;
    }
  }
  return void 0;
}
function findIndex(array, predicate, startIndex) {
  if (array === void 0) return -1;
  for (let i = startIndex ?? 0; i < array.length; i++) {
    if (predicate(array[i], i)) {
      return i;
    }
  }
  return -1;
}
function findLastIndex(array, predicate, startIndex) {
  if (array === void 0) return -1;
  for (let i = startIndex ?? array.length - 1; i >= 0; i--) {
    if (predicate(array[i], i)) {
      return i;
    }
  }
  return -1;
}
function contains(array, value, equalityComparer = equateValues) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      if (equalityComparer(array[i], value)) {
        return true;
      }
    }
  }
  return false;
}
function indexOfAnyCharCode(text, charCodes, start) {
  for (let i = start ?? 0; i < text.length; i++) {
    if (contains(charCodes, text.charCodeAt(i))) {
      return i;
    }
  }
  return -1;
}
function countWhere(array, predicate) {
  let count = 0;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = array[i];
      if (predicate(v, i)) {
        count++;
      }
    }
  }
  return count;
}
function filter(array, f) {
  if (array !== void 0) {
    const len = array.length;
    let i = 0;
    while (i < len && f(array[i])) i++;
    if (i < len) {
      const result = array.slice(0, i);
      i++;
      while (i < len) {
        const item = array[i];
        if (f(item)) {
          result.push(item);
        }
        i++;
      }
      return result;
    }
  }
  return array;
}
function filterMutate(array, f) {
  let outIndex = 0;
  for (let i = 0; i < array.length; i++) {
    if (f(array[i], i, array)) {
      array[outIndex] = array[i];
      outIndex++;
    }
  }
  array.length = outIndex;
}
function clear(array) {
  array.length = 0;
}
function map(array, f) {
  let result;
  if (array !== void 0) {
    result = [];
    for (let i = 0; i < array.length; i++) {
      result.push(f(array[i], i));
    }
  }
  return result;
}
function* mapIterator(iter, mapFn) {
  for (const x of iter) {
    yield mapFn(x);
  }
}
function sameMap(array, f) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      const mapped = f(item, i);
      if (item !== mapped) {
        const result = array.slice(0, i);
        result.push(mapped);
        for (i++; i < array.length; i++) {
          result.push(f(array[i], i));
        }
        return result;
      }
    }
  }
  return array;
}
function flatten(array) {
  const result = [];
  for (let i = 0; i < array.length; i++) {
    const v = array[i];
    if (v) {
      if (isArray(v)) {
        addRange(result, v);
      } else {
        result.push(v);
      }
    }
  }
  return result;
}
function flatMap(array, mapfn) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = mapfn(array[i], i);
      if (v) {
        if (isArray(v)) {
          result = addRange(result, v);
        } else {
          result = append(result, v);
        }
      }
    }
  }
  return result ?? emptyArray;
}
function flatMapToMutable(array, mapfn) {
  const result = [];
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = mapfn(array[i], i);
      if (v) {
        if (isArray(v)) {
          addRange(result, v);
        } else {
          result.push(v);
        }
      }
    }
  }
  return result;
}
function sameFlatMap(array, mapfn) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      const mapped = mapfn(item, i);
      if (result || item !== mapped || isArray(mapped)) {
        if (!result) {
          result = array.slice(0, i);
        }
        if (isArray(mapped)) {
          addRange(result, mapped);
        } else {
          result.push(mapped);
        }
      }
    }
  }
  return result ?? array;
}
function mapDefined(array, mapFn) {
  const result = [];
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const mapped = mapFn(array[i], i);
      if (mapped !== void 0) {
        result.push(mapped);
      }
    }
  }
  return result;
}
function* mapDefinedIterator(iter, mapFn) {
  for (const x of iter) {
    const value = mapFn(x);
    if (value !== void 0) {
      yield value;
    }
  }
}
function getOrUpdate(map2, key, callback) {
  if (map2.has(key)) {
    return map2.get(key);
  }
  const value = callback();
  map2.set(key, value);
  return value;
}
function tryAddToSet(set, value) {
  if (!set.has(value)) {
    set.add(value);
    return true;
  }
  return false;
}
function spanMap(array, keyfn, mapfn) {
  let result;
  if (array !== void 0) {
    result = [];
    const len = array.length;
    let previousKey;
    let key;
    let start = 0;
    let pos = 0;
    while (start < len) {
      while (pos < len) {
        const value = array[pos];
        key = keyfn(value, pos);
        if (pos === 0) {
          previousKey = key;
        } else if (key !== previousKey) {
          break;
        }
        pos++;
      }
      if (start < pos) {
        const v = mapfn(array.slice(start, pos), previousKey, start, pos);
        if (v) {
          result.push(v);
        }
        start = pos;
      }
      previousKey = key;
      pos++;
    }
  }
  return result;
}
function some(array, predicate) {
  if (array !== void 0) {
    if (predicate !== void 0) {
      for (let i = 0; i < array.length; i++) {
        if (predicate(array[i])) {
          return true;
        }
      }
    } else {
      return array.length > 0;
    }
  }
  return false;
}
function getRangesWhere(arr, pred, cb) {
  let start;
  for (let i = 0; i < arr.length; i++) {
    if (pred(arr[i])) {
      start = start === void 0 ? i : start;
    } else {
      if (start !== void 0) {
        cb(start, i);
        start = void 0;
      }
    }
  }
  if (start !== void 0) cb(start, arr.length);
}
function concatenate(array1, array2) {
  if (array2 === void 0 || array2.length === 0) return array1;
  if (array1 === void 0 || array1.length === 0) return array2;
  return [...array1, ...array2];
}
function selectIndex(_, i) {
  return i;
}
function indicesOf(array) {
  return array.map(selectIndex);
}
function deduplicateRelational(array, equalityComparer, comparer) {
  const indices = indicesOf(array);
  stableSortIndices(array, indices, comparer);
  let last2 = array[indices[0]];
  const deduplicated = [indices[0]];
  for (let i = 1; i < indices.length; i++) {
    const index = indices[i];
    const item = array[index];
    if (!equalityComparer(last2, item)) {
      deduplicated.push(index);
      last2 = item;
    }
  }
  deduplicated.sort();
  return deduplicated.map((i) => array[i]);
}
function deduplicateEquality(array, equalityComparer) {
  const result = [];
  for (let i = 0; i < array.length; i++) {
    pushIfUnique(result, array[i], equalityComparer);
  }
  return result;
}
function deduplicate(array, equalityComparer, comparer) {
  return array.length === 0 ? [] : array.length === 1 ? array.slice() : comparer ? deduplicateRelational(array, equalityComparer, comparer) : deduplicateEquality(array, equalityComparer);
}
function deduplicateSorted(array, comparer) {
  if (array.length === 0) return emptyArray;
  let last2 = array[0];
  const deduplicated = [last2];
  for (let i = 1; i < array.length; i++) {
    const next = array[i];
    switch (comparer(next, last2)) {
      // equality comparison
      case true:
      // relational comparison
      // falls through
      case 0 /* EqualTo */:
        continue;
      case -1 /* LessThan */:
        return Debug.fail("Array is unsorted.");
    }
    deduplicated.push(last2 = next);
  }
  return deduplicated;
}
function insertSorted(array, insert, compare, equalityComparer, allowDuplicates) {
  if (array.length === 0) {
    array.push(insert);
    return true;
  }
  const insertIndex = binarySearch(array, insert, identity, compare);
  if (insertIndex < 0) {
    if (equalityComparer && !allowDuplicates) {
      const idx = ~insertIndex;
      if (idx > 0 && equalityComparer(insert, array[idx - 1])) {
        return false;
      }
      if (idx < array.length && equalityComparer(insert, array[idx])) {
        array.splice(idx, 1, insert);
        return true;
      }
    }
    array.splice(~insertIndex, 0, insert);
    return true;
  }
  if (allowDuplicates) {
    array.splice(insertIndex, 0, insert);
    return true;
  }
  return false;
}
function sortAndDeduplicate(array, comparer, equalityComparer) {
  return deduplicateSorted(toSorted(array, comparer), equalityComparer ?? comparer ?? compareStringsCaseSensitive);
}
function arrayIsEqualTo(array1, array2, equalityComparer = equateValues) {
  if (array1 === void 0 || array2 === void 0) {
    return array1 === array2;
  }
  if (array1.length !== array2.length) {
    return false;
  }
  for (let i = 0; i < array1.length; i++) {
    if (!equalityComparer(array1[i], array2[i], i)) {
      return false;
    }
  }
  return true;
}
function compact(array) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = array[i];
      if (result ?? !v) {
        result ?? (result = array.slice(0, i));
        if (v) {
          result.push(v);
        }
      }
    }
  }
  return result ?? array;
}
function relativeComplement(arrayA, arrayB, comparer) {
  if (!arrayB || !arrayA || arrayB.length === 0 || arrayA.length === 0) return arrayB;
  const result = [];
  loopB:
    for (let offsetA = 0, offsetB = 0; offsetB < arrayB.length; offsetB++) {
      if (offsetB > 0) {
        Debug.assertGreaterThanOrEqual(comparer(arrayB[offsetB], arrayB[offsetB - 1]), 0 /* EqualTo */);
      }
      loopA:
        for (const startA = offsetA; offsetA < arrayA.length; offsetA++) {
          if (offsetA > startA) {
            Debug.assertGreaterThanOrEqual(comparer(arrayA[offsetA], arrayA[offsetA - 1]), 0 /* EqualTo */);
          }
          switch (comparer(arrayB[offsetB], arrayA[offsetA])) {
            case -1 /* LessThan */:
              result.push(arrayB[offsetB]);
              continue loopB;
            case 0 /* EqualTo */:
              continue loopB;
            case 1 /* GreaterThan */:
              continue loopA;
          }
        }
    }
  return result;
}
function append(to, value) {
  if (value === void 0) return to;
  if (to === void 0) return [value];
  to.push(value);
  return to;
}
function toOffset(array, offset) {
  return offset < 0 ? array.length + offset : offset;
}
function addRange(to, from, start, end) {
  if (from === void 0 || from.length === 0) return to;
  if (to === void 0) return from.slice(start, end);
  start = start === void 0 ? 0 : toOffset(from, start);
  end = end === void 0 ? from.length : toOffset(from, end);
  for (let i = start; i < end && i < from.length; i++) {
    if (from[i] !== void 0) {
      to.push(from[i]);
    }
  }
  return to;
}
function pushIfUnique(array, toAdd, equalityComparer) {
  if (contains(array, toAdd, equalityComparer)) {
    return false;
  } else {
    array.push(toAdd);
    return true;
  }
}
function appendIfUnique(array, toAdd, equalityComparer) {
  if (array !== void 0) {
    pushIfUnique(array, toAdd, equalityComparer);
    return array;
  } else {
    return [toAdd];
  }
}
function stableSortIndices(array, indices, comparer) {
  indices.sort((x, y) => comparer(array[x], array[y]) || compareValues(x, y));
}
function toSorted(array, comparer) {
  return array.length === 0 ? emptyArray : array.slice().sort(comparer);
}
function rangeEquals(array1, array2, pos, end) {
  while (pos < end) {
    if (array1[pos] !== array2[pos]) {
      return false;
    }
    pos++;
  }
  return true;
}
var elementAt = !!Array.prototype.at ? (array, offset) => array == null ? void 0 : array.at(offset) : (array, offset) => {
  if (array !== void 0) {
    offset = toOffset(array, offset);
    if (offset < array.length) {
      return array[offset];
    }
  }
  return void 0;
};
function firstOrUndefined(array) {
  return array === void 0 || array.length === 0 ? void 0 : array[0];
}
function firstOrUndefinedIterator(iter) {
  if (iter !== void 0) {
    for (const value of iter) {
      return value;
    }
  }
  return void 0;
}
function first(array) {
  Debug.assert(array.length !== 0);
  return array[0];
}
function firstIterator(iter) {
  for (const value of iter) {
    return value;
  }
  Debug.fail("iterator is empty");
}
function lastOrUndefined(array) {
  return array === void 0 || array.length === 0 ? void 0 : array[array.length - 1];
}
function last(array) {
  Debug.assert(array.length !== 0);
  return array[array.length - 1];
}
function singleOrUndefined(array) {
  return array !== void 0 && array.length === 1 ? array[0] : void 0;
}
function singleOrMany(array) {
  return array !== void 0 && array.length === 1 ? array[0] : array;
}
function replaceElement(array, index, value) {
  const result = array.slice(0);
  result[index] = value;
  return result;
}
function binarySearch(array, value, keySelector, keyComparer, offset) {
  return binarySearchKey(array, keySelector(value), keySelector, keyComparer, offset);
}
function binarySearchKey(array, key, keySelector, keyComparer, offset) {
  if (!some(array)) {
    return -1;
  }
  let low = offset ?? 0;
  let high = array.length - 1;
  while (low <= high) {
    const middle = low + (high - low >> 1);
    const midKey = keySelector(array[middle], middle);
    switch (keyComparer(midKey, key)) {
      case -1 /* LessThan */:
        low = middle + 1;
        break;
      case 0 /* EqualTo */:
        return middle;
      case 1 /* GreaterThan */:
        high = middle - 1;
        break;
    }
  }
  return ~low;
}
function reduceLeft(array, f, initial, start, count) {
  if (array && array.length > 0) {
    const size = array.length;
    if (size > 0) {
      let pos = start === void 0 || start < 0 ? 0 : start;
      const end = count === void 0 || pos + count > size - 1 ? size - 1 : pos + count;
      let result;
      if (arguments.length <= 2) {
        result = array[pos];
        pos++;
      } else {
        result = initial;
      }
      while (pos <= end) {
        result = f(result, array[pos], pos);
        pos++;
      }
      return result;
    }
  }
  return initial;
}
var hasOwnProperty = Object.prototype.hasOwnProperty;
function hasProperty(map2, key) {
  return hasOwnProperty.call(map2, key);
}
function getOwnKeys(map2) {
  const keys = [];
  for (const key in map2) {
    if (hasOwnProperty.call(map2, key)) {
      keys.push(key);
    }
  }
  return keys;
}
function getOwnValues(collection) {
  const values = [];
  for (const key in collection) {
    if (hasOwnProperty.call(collection, key)) {
      values.push(collection[key]);
    }
  }
  return values;
}
function arrayOf(count, f) {
  const result = new Array(count);
  for (let i = 0; i < count; i++) {
    result[i] = f(i);
  }
  return result;
}
function arrayFrom(iterator, map2) {
  const result = [];
  for (const value of iterator) {
    result.push(map2 ? map2(value) : value);
  }
  return result;
}
function assign(t, ...args) {
  for (const arg of args) {
    if (arg === void 0) continue;
    for (const p in arg) {
      if (hasProperty(arg, p)) {
        t[p] = arg[p];
      }
    }
  }
  return t;
}
function equalOwnProperties(left, right, equalityComparer = equateValues) {
  if (left === right) return true;
  if (!left || !right) return false;
  for (const key in left) {
    if (hasOwnProperty.call(left, key)) {
      if (!hasOwnProperty.call(right, key)) return false;
      if (!equalityComparer(left[key], right[key])) return false;
    }
  }
  for (const key in right) {
    if (hasOwnProperty.call(right, key)) {
      if (!hasOwnProperty.call(left, key)) return false;
    }
  }
  return true;
}
function arrayToMap(array, makeKey, makeValue = identity) {
  const result = /* @__PURE__ */ new Map();
  for (let i = 0; i < array.length; i++) {
    const value = array[i];
    const key = makeKey(value);
    if (key !== void 0) result.set(key, makeValue(value));
  }
  return result;
}
function arrayToMultiMap(values, makeKey, makeValue = identity) {
  const result = createMultiMap();
  for (let i = 0; i < values.length; i++) {
    const value = values[i];
    result.add(makeKey(value), makeValue(value));
  }
  return result;
}
function group(values, getGroupId, resultSelector = identity) {
  return arrayFrom(arrayToMultiMap(values, getGroupId).values(), resultSelector);
}
function groupBy(values, keySelector) {
  const result = {};
  if (values !== void 0) {
    for (let i = 0; i < values.length; i++) {
      const value = values[i];
      const key = `${keySelector(value)}`;
      const array = result[key] ?? (result[key] = []);
      array.push(value);
    }
  }
  return result;
}
function extend(first2, second) {
  const result = {};
  for (const id in second) {
    if (hasOwnProperty.call(second, id)) {
      result[id] = second[id];
    }
  }
  for (const id in first2) {
    if (hasOwnProperty.call(first2, id)) {
      result[id] = first2[id];
    }
  }
  return result;
}
function copyProperties(first2, second) {
  for (const id in second) {
    if (hasOwnProperty.call(second, id)) {
      first2[id] = second[id];
    }
  }
}
function maybeBind(obj, fn) {
  return fn == null ? void 0 : fn.bind(obj);
}
function createMultiMap() {
  const map2 = /* @__PURE__ */ new Map();
  map2.add = multiMapAdd;
  map2.remove = multiMapRemove;
  return map2;
}
function multiMapAdd(key, value) {
  let values = this.get(key);
  if (values !== void 0) {
    values.push(value);
  } else {
    this.set(key, values = [value]);
  }
  return values;
}
function multiMapRemove(key, value) {
  const values = this.get(key);
  if (values !== void 0) {
    unorderedRemoveItem(values, value);
    if (!values.length) {
      this.delete(key);
    }
  }
}
function createQueue(items) {
  const elements = (items == null ? void 0 : items.slice()) ?? [];
  let headIndex = 0;
  function isEmpty() {
    return headIndex === elements.length;
  }
  function enqueue(...items2) {
    elements.push(...items2);
  }
  function dequeue() {
    if (isEmpty()) {
      throw new Error("Queue is empty");
    }
    const result = elements[headIndex];
    elements[headIndex] = void 0;
    headIndex++;
    if (headIndex > 100 && headIndex > elements.length >> 1) {
      const newLength = elements.length - headIndex;
      elements.copyWithin(
        /*target*/
        0,
        /*start*/
        headIndex
      );
      elements.length = newLength;
      headIndex = 0;
    }
    return result;
  }
  return {
    enqueue,
    dequeue,
    isEmpty
  };
}
function isArray(value) {
  return Array.isArray(value);
}
function toArray(value) {
  return isArray(value) ? value : [value];
}
function isString(text) {
  return typeof text === "string";
}
function isNumber(x) {
  return typeof x === "number";
}
function tryCast(value, test) {
  return value !== void 0 && test(value) ? value : void 0;
}
function cast(value, test) {
  if (value !== void 0 && test(value)) return value;
  return Debug.fail(`Invalid cast. The supplied value ${value} did not pass the test '${Debug.getFunctionName(test)}'.`);
}
function noop(_) {
}
function returnFalse() {
  return false;
}
function returnTrue() {
  return true;
}
function returnUndefined() {
  return void 0;
}
function identity(x) {
  return x;
}
function toLowerCase(x) {
  return x.toLowerCase();
}
var fileNameLowerCaseRegExp = /[^\u0130\u0131\u00DFa-z0-9\\/:\-_. ]+/g;
function toFileNameLowerCase(x) {
  return fileNameLowerCaseRegExp.test(x) ? x.replace(fileNameLowerCaseRegExp, toLowerCase) : x;
}
function notImplemented() {
  throw new Error("Not implemented");
}
function memoize(callback) {
  let value;
  return () => {
    if (callback) {
      value = callback();
      callback = void 0;
    }
    return value;
  };
}
function memoizeOne(callback) {
  const map2 = /* @__PURE__ */ new Map();
  return (arg) => {
    const key = `${typeof arg}:${arg}`;
    let value = map2.get(key);
    if (value === void 0 && !map2.has(key)) {
      value = callback(arg);
      map2.set(key, value);
    }
    return value;
  };
}
function equateValues(a, b) {
  return a === b;
}
function equateStringsCaseInsensitive(a, b) {
  return a === b || a !== void 0 && b !== void 0 && a.toUpperCase() === b.toUpperCase();
}
function equateStringsCaseSensitive(a, b) {
  return equateValues(a, b);
}
function compareComparableValues(a, b) {
  return a === b ? 0 /* EqualTo */ : a === void 0 ? -1 /* LessThan */ : b === void 0 ? 1 /* GreaterThan */ : a < b ? -1 /* LessThan */ : 1 /* GreaterThan */;
}
function compareValues(a, b) {
  return compareComparableValues(a, b);
}
function maxBy(arr, init, mapper) {
  for (let i = 0; i < arr.length; i++) {
    init = Math.max(init, mapper(arr[i]));
  }
  return init;
}
function min(items, compare) {
  return reduceLeft(items, (x, y) => compare(x, y) === -1 /* LessThan */ ? x : y);
}
function compareStringsCaseInsensitive(a, b) {
  if (a === b) return 0 /* EqualTo */;
  if (a === void 0) return -1 /* LessThan */;
  if (b === void 0) return 1 /* GreaterThan */;
  a = a.toUpperCase();
  b = b.toUpperCase();
  return a < b ? -1 /* LessThan */ : a > b ? 1 /* GreaterThan */ : 0 /* EqualTo */;
}
function compareStringsCaseSensitive(a, b) {
  return compareComparableValues(a, b);
}
function getStringComparer(ignoreCase) {
  return ignoreCase ? compareStringsCaseInsensitive : compareStringsCaseSensitive;
}
var uiComparerCaseSensitive;
var uiLocale;
function setUILocale(value) {
  if (uiLocale !== value) {
    uiLocale = value;
    uiComparerCaseSensitive = void 0;
  }
}
function compareBooleans(a, b) {
  return compareValues(a ? 1 : 0, b ? 1 : 0);
}
function getSpellingSuggestion(name, candidates, getName) {
  const maximumLengthDifference = Math.max(2, Math.floor(name.length * 0.34));
  let bestDistance = Math.floor(name.length * 0.4) + 1;
  let bestCandidate;
  for (const candidate of candidates) {
    const candidateName = getName(candidate);
    if (candidateName !== void 0 && Math.abs(candidateName.length - name.length) <= maximumLengthDifference) {
      if (candidateName === name) {
        continue;
      }
      if (candidateName.length < 3 && candidateName.toLowerCase() !== name.toLowerCase()) {
        continue;
      }
      const distance = levenshteinWithMax(name, candidateName, bestDistance - 0.1);
      if (distance === void 0) {
        continue;
      }
      Debug.assert(distance < bestDistance);
      bestDistance = distance;
      bestCandidate = candidate;
    }
  }
  return bestCandidate;
}
function levenshteinWithMax(s1, s2, max) {
  let previous = new Array(s2.length + 1);
  let current = new Array(s2.length + 1);
  const big = max + 0.01;
  for (let i = 0; i <= s2.length; i++) {
    previous[i] = i;
  }
  for (let i = 1; i <= s1.length; i++) {
    const c1 = s1.charCodeAt(i - 1);
    const minJ = Math.ceil(i > max ? i - max : 1);
    const maxJ = Math.floor(s2.length > max + i ? max + i : s2.length);
    current[0] = i;
    let colMin = i;
    for (let j = 1; j < minJ; j++) {
      current[j] = big;
    }
    for (let j = minJ; j <= maxJ; j++) {
      const substitutionDistance = s1[i - 1].toLowerCase() === s2[j - 1].toLowerCase() ? previous[j - 1] + 0.1 : previous[j - 1] + 2;
      const dist = c1 === s2.charCodeAt(j - 1) ? previous[j - 1] : Math.min(
        /*delete*/
        previous[j] + 1,
        /*insert*/
        current[j - 1] + 1,
        /*substitute*/
        substitutionDistance
      );
      current[j] = dist;
      colMin = Math.min(colMin, dist);
    }
    for (let j = maxJ + 1; j <= s2.length; j++) {
      current[j] = big;
    }
    if (colMin > max) {
      return void 0;
    }
    const temp = previous;
    previous = current;
    current = temp;
  }
  const res = previous[s2.length];
  return res > max ? void 0 : res;
}
function endsWith(str, suffix, ignoreCase) {
  const expectedPos = str.length - suffix.length;
  return expectedPos >= 0 && (ignoreCase ? equateStringsCaseInsensitive(str.slice(expectedPos), suffix) : str.indexOf(suffix, expectedPos) === expectedPos);
}
function removeSuffix(str, suffix) {
  return endsWith(str, suffix) ? str.slice(0, str.length - suffix.length) : str;
}
function orderedRemoveItem(array, item) {
  for (let i = 0; i < array.length; i++) {
    if (array[i] === item) {
      orderedRemoveItemAt(array, i);
      return true;
    }
  }
  return false;
}
function orderedRemoveItemAt(array, index) {
  for (let i = index; i < array.length - 1; i++) {
    array[i] = array[i + 1];
  }
  array.pop();
}
function unorderedRemoveItemAt(array, index) {
  array[index] = array[array.length - 1];
  array.pop();
}
function unorderedRemoveItem(array, item) {
  return unorderedRemoveFirstItemWhere(array, (element) => element === item);
}
function unorderedRemoveFirstItemWhere(array, predicate) {
  for (let i = 0; i < array.length; i++) {
    if (predicate(array[i])) {
      unorderedRemoveItemAt(array, i);
      return true;
    }
  }
  return false;
}
function createGetCanonicalFileName(useCaseSensitiveFileNames2) {
  return useCaseSensitiveFileNames2 ? identity : toFileNameLowerCase;
}
function patternText({ prefix, suffix }) {
  return `${prefix}*${suffix}`;
}
function matchedText(pattern, candidate) {
  Debug.assert(isPatternMatch(pattern, candidate));
  return candidate.substring(pattern.prefix.length, candidate.length - pattern.suffix.length);
}
function findBestPatternMatch(values, getPattern, candidate) {
  let matchedValue;
  let longestMatchPrefixLength = -1;
  for (let i = 0; i < values.length; i++) {
    const v = values[i];
    const pattern = getPattern(v);
    if (pattern.prefix.length > longestMatchPrefixLength && isPatternMatch(pattern, candidate)) {
      longestMatchPrefixLength = pattern.prefix.length;
      matchedValue = v;
    }
  }
  return matchedValue;
}
function startsWith(str, prefix, ignoreCase) {
  return ignoreCase ? equateStringsCaseInsensitive(str.slice(0, prefix.length), prefix) : str.lastIndexOf(prefix, 0) === 0;
}
function removePrefix(str, prefix) {
  return startsWith(str, prefix) ? str.substr(prefix.length) : str;
}
function isPatternMatch({ prefix, suffix }, candidate) {
  return candidate.length >= prefix.length + suffix.length && startsWith(candidate, prefix) && endsWith(candidate, suffix);
}
function and(f, g) {
  return (arg) => f(arg) && g(arg);
}
function or(...fs) {
  return (...args) => {
    let lastResult;
    for (const f of fs) {
      lastResult = f(...args);
      if (lastResult) {
        return lastResult;
      }
    }
    return lastResult;
  };
}
function not(fn) {
  return (...args) => !fn(...args);
}
function assertType(_) {
}
function singleElementArray(t) {
  return t === void 0 ? void 0 : [t];
}
function enumerateInsertsAndDeletes(newItems, oldItems, comparer, inserted, deleted, unchanged) {
  unchanged ?? (unchanged = noop);
  let newIndex = 0;
  let oldIndex = 0;
  const newLen = newItems.length;
  const oldLen = oldItems.length;
  let hasChanges = false;
  while (newIndex < newLen && oldIndex < oldLen) {
    const newItem = newItems[newIndex];
    const oldItem = oldItems[oldIndex];
    const compareResult = comparer(newItem, oldItem);
    if (compareResult === -1 /* LessThan */) {
      inserted(newItem);
      newIndex++;
      hasChanges = true;
    } else if (compareResult === 1 /* GreaterThan */) {
      deleted(oldItem);
      oldIndex++;
      hasChanges = true;
    } else {
      unchanged(oldItem, newItem);
      newIndex++;
      oldIndex++;
    }
  }
  while (newIndex < newLen) {
    inserted(newItems[newIndex++]);
    hasChanges = true;
  }
  while (oldIndex < oldLen) {
    deleted(oldItems[oldIndex++]);
    hasChanges = true;
  }
  return hasChanges;
}
function cartesianProduct(arrays) {
  const result = [];
  cartesianProductWorker(
    arrays,
    result,
    /*outer*/
    void 0,
    0
  );
  return result;
}
function cartesianProductWorker(arrays, result, outer, index) {
  for (const element of arrays[index]) {
    let inner;
    if (outer) {
      inner = outer.slice();
      inner.push(element);
    } else {
      inner = [element];
    }
    if (index === arrays.length - 1) {
      result.push(inner);
    } else {
      cartesianProductWorker(arrays, result, inner, index + 1);
    }
  }
}
function takeWhile(array, predicate) {
  if (array !== void 0) {
    const len = array.length;
    let index = 0;
    while (index < len && predicate(array[index])) {
      index++;
    }
    return array.slice(0, index);
  }
}
function skipWhile(array, predicate) {
  if (array !== void 0) {
    const len = array.length;
    let index = 0;
    while (index < len && predicate(array[index])) {
      index++;
    }
    return array.slice(index);
  }
}
function isNodeLikeSystem() {
  return typeof process !== "undefined" && !!process.nextTick && !process.browser && typeof require !== "undefined";
}

// src/compiler/debug.ts
var Debug;
((Debug2) => {
  let currentAssertionLevel = 0 /* None */;
  Debug2.currentLogLevel = 2 /* Warning */;
  Debug2.isDebugging = false;
  function shouldLog(level) {
    return Debug2.currentLogLevel <= level;
  }
  Debug2.shouldLog = shouldLog;
  function logMessage(level, s) {
    if (Debug2.loggingHost && shouldLog(level)) {
      Debug2.loggingHost.log(level, s);
    }
  }
  function log(s) {
    logMessage(3 /* Info */, s);
  }
  Debug2.log = log;
  ((_log) => {
    function error(s) {
      logMessage(1 /* Error */, s);
    }
    _log.error = error;
    function warn(s) {
      logMessage(2 /* Warning */, s);
    }
    _log.warn = warn;
    function log2(s) {
      logMessage(3 /* Info */, s);
    }
    _log.log = log2;
    function trace2(s) {
      logMessage(4 /* Verbose */, s);
    }
    _log.trace = trace2;
  })(log = Debug2.log || (Debug2.log = {}));
  const assertionCache = {};
  function getAssertionLevel() {
    return currentAssertionLevel;
  }
  Debug2.getAssertionLevel = getAssertionLevel;
  function setAssertionLevel(level) {
    const prevAssertionLevel = currentAssertionLevel;
    currentAssertionLevel = level;
    if (level > prevAssertionLevel) {
      for (const key of getOwnKeys(assertionCache)) {
        const cachedFunc = assertionCache[key];
        if (cachedFunc !== void 0 && Debug2[key] !== cachedFunc.assertion && level >= cachedFunc.level) {
          Debug2[key] = cachedFunc;
          assertionCache[key] = void 0;
        }
      }
    }
  }
  Debug2.setAssertionLevel = setAssertionLevel;
  function shouldAssert(level) {
    return currentAssertionLevel >= level;
  }
  Debug2.shouldAssert = shouldAssert;
  function shouldAssertFunction(level, name) {
    if (!shouldAssert(level)) {
      assertionCache[name] = { level, assertion: Debug2[name] };
      Debug2[name] = noop;
      return false;
    }
    return true;
  }
  function fail(message, stackCrawlMark) {
    debugger;
    const e = new Error(message ? `Debug Failure. ${message}` : "Debug Failure.");
    if (Error.captureStackTrace) {
      Error.captureStackTrace(e, stackCrawlMark || fail);
    }
    throw e;
  }
  Debug2.fail = fail;
  function failBadSyntaxKind(node, message, stackCrawlMark) {
    return fail(
      `${message || "Unexpected node."}\r
Node ${formatSyntaxKind(node.kind)} was unexpected.`,
      stackCrawlMark || failBadSyntaxKind
    );
  }
  Debug2.failBadSyntaxKind = failBadSyntaxKind;
  function assert(expression, message, verboseDebugInfo, stackCrawlMark) {
    if (!expression) {
      message = message ? `False expression: ${message}` : "False expression.";
      if (verboseDebugInfo) {
        message += "\r\nVerbose Debug Information: " + (typeof verboseDebugInfo === "string" ? verboseDebugInfo : verboseDebugInfo());
      }
      fail(message, stackCrawlMark || assert);
    }
  }
  Debug2.assert = assert;
  function assertEqual(a, b, msg, msg2, stackCrawlMark) {
    if (a !== b) {
      const message = msg ? msg2 ? `${msg} ${msg2}` : msg : "";
      fail(`Expected ${a} === ${b}. ${message}`, stackCrawlMark || assertEqual);
    }
  }
  Debug2.assertEqual = assertEqual;
  function assertLessThan(a, b, msg, stackCrawlMark) {
    if (a >= b) {
      fail(`Expected ${a} < ${b}. ${msg || ""}`, stackCrawlMark || assertLessThan);
    }
  }
  Debug2.assertLessThan = assertLessThan;
  function assertLessThanOrEqual(a, b, stackCrawlMark) {
    if (a > b) {
      fail(`Expected ${a} <= ${b}`, stackCrawlMark || assertLessThanOrEqual);
    }
  }
  Debug2.assertLessThanOrEqual = assertLessThanOrEqual;
  function assertGreaterThanOrEqual(a, b, stackCrawlMark) {
    if (a < b) {
      fail(`Expected ${a} >= ${b}`, stackCrawlMark || assertGreaterThanOrEqual);
    }
  }
  Debug2.assertGreaterThanOrEqual = assertGreaterThanOrEqual;
  function assertIsDefined(value, message, stackCrawlMark) {
    if (value === void 0 || value === null) {
      fail(message, stackCrawlMark || assertIsDefined);
    }
  }
  Debug2.assertIsDefined = assertIsDefined;
  function checkDefined(value, message, stackCrawlMark) {
    assertIsDefined(value, message, stackCrawlMark || checkDefined);
    return value;
  }
  Debug2.checkDefined = checkDefined;
  function assertEachIsDefined(value, message, stackCrawlMark) {
    for (const v of value) {
      assertIsDefined(v, message, stackCrawlMark || assertEachIsDefined);
    }
  }
  Debug2.assertEachIsDefined = assertEachIsDefined;
  function checkEachDefined(value, message, stackCrawlMark) {
    assertEachIsDefined(value, message, stackCrawlMark || checkEachDefined);
    return value;
  }
  Debug2.checkEachDefined = checkEachDefined;
  function assertNever(member, message = "Illegal value:", stackCrawlMark) {
    const detail = typeof member === "object" && hasProperty(member, "kind") && hasProperty(member, "pos") ? "SyntaxKind: " + formatSyntaxKind(member.kind) : JSON.stringify(member);
    return fail(`${message} ${detail}`, stackCrawlMark || assertNever);
  }
  Debug2.assertNever = assertNever;
  function assertEachNode(nodes, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertEachNode")) {
      assert(
        test === void 0 || every(nodes, test),
        message || "Unexpected node.",
        () => `Node array did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertEachNode
      );
    }
  }
  Debug2.assertEachNode = assertEachNode;
  function assertNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertNode")) {
      assert(
        node !== void 0 && (test === void 0 || test(node)),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertNode
      );
    }
  }
  Debug2.assertNode = assertNode;
  function assertNotNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertNotNode")) {
      assert(
        node === void 0 || test === void 0 || !test(node),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node.kind)} should not have passed test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertNotNode
      );
    }
  }
  Debug2.assertNotNode = assertNotNode;
  function assertOptionalNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertOptionalNode")) {
      assert(
        test === void 0 || node === void 0 || test(node),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertOptionalNode
      );
    }
  }
  Debug2.assertOptionalNode = assertOptionalNode;
  function assertOptionalToken(node, kind, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertOptionalToken")) {
      assert(
        kind === void 0 || node === void 0 || node.kind === kind,
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} was not a '${formatSyntaxKind(kind)}' token.`,
        stackCrawlMark || assertOptionalToken
      );
    }
  }
  Debug2.assertOptionalToken = assertOptionalToken;
  function assertMissingNode(node, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertMissingNode")) {
      assert(
        node === void 0,
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node.kind)} was unexpected'.`,
        stackCrawlMark || assertMissingNode
      );
    }
  }
  Debug2.assertMissingNode = assertMissingNode;
  function type(_value) {
  }
  Debug2.type = type;
  function getFunctionName(func) {
    if (typeof func !== "function") {
      return "";
    } else if (hasProperty(func, "name")) {
      return func.name;
    } else {
      const text = Function.prototype.toString.call(func);
      const match = /^function\s+([\w$]+)\s*\(/.exec(text);
      return match ? match[1] : "";
    }
  }
  Debug2.getFunctionName = getFunctionName;
  function formatSymbol(symbol) {
    return `{ name: ${unescapeLeadingUnderscores(symbol.escapedName)}; flags: ${formatSymbolFlags(symbol.flags)}; declarations: ${map(symbol.declarations, (node) => formatSyntaxKind(node.kind))} }`;
  }
  Debug2.formatSymbol = formatSymbol;
  function formatEnum(value = 0, enumObject, isFlags) {
    const members = getEnumMembers(enumObject);
    if (value === 0) {
      return members.length > 0 && members[0][0] === 0 ? members[0][1] : "0";
    }
    if (isFlags) {
      const result = [];
      let remainingFlags = value;
      for (const [enumValue, enumName] of members) {
        if (enumValue > value) {
          break;
        }
        if (enumValue !== 0 && enumValue & value) {
          result.push(enumName);
          remainingFlags &= ~enumValue;
        }
      }
      if (remainingFlags === 0) {
        return result.join("|");
      }
    } else {
      for (const [enumValue, enumName] of members) {
        if (enumValue === value) {
          return enumName;
        }
      }
    }
    return value.toString();
  }
  Debug2.formatEnum = formatEnum;
  const enumMemberCache = /* @__PURE__ */ new Map();
  function getEnumMembers(enumObject) {
    const existing = enumMemberCache.get(enumObject);
    if (existing) {
      return existing;
    }
    const result = [];
    for (const name in enumObject) {
      const value = enumObject[name];
      if (typeof value === "number") {
        result.push([value, name]);
      }
    }
    const sorted = toSorted(result, (x, y) => compareValues(x[0], y[0]));
    enumMemberCache.set(enumObject, sorted);
    return sorted;
  }
  function formatSyntaxKind(kind) {
    return formatEnum(
      kind,
      SyntaxKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatSyntaxKind = formatSyntaxKind;
  function formatSnippetKind(kind) {
    return formatEnum(
      kind,
      SnippetKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatSnippetKind = formatSnippetKind;
  function formatScriptKind(kind) {
    return formatEnum(
      kind,
      ScriptKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatScriptKind = formatScriptKind;
  function formatNodeFlags(flags) {
    return formatEnum(
      flags,
      NodeFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatNodeFlags = formatNodeFlags;
  function formatNodeCheckFlags(flags) {
    return formatEnum(
      flags,
      NodeCheckFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatNodeCheckFlags = formatNodeCheckFlags;
  function formatModifierFlags(flags) {
    return formatEnum(
      flags,
      ModifierFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatModifierFlags = formatModifierFlags;
  function formatTransformFlags(flags) {
    return formatEnum(
      flags,
      TransformFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTransformFlags = formatTransformFlags;
  function formatEmitFlags(flags) {
    return formatEnum(
      flags,
      EmitFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatEmitFlags = formatEmitFlags;
  function formatSymbolFlags(flags) {
    return formatEnum(
      flags,
      SymbolFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSymbolFlags = formatSymbolFlags;
  function formatTypeFlags(flags) {
    return formatEnum(
      flags,
      TypeFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTypeFlags = formatTypeFlags;
  function formatSignatureFlags(flags) {
    return formatEnum(
      flags,
      SignatureFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSignatureFlags = formatSignatureFlags;
  function formatObjectFlags(flags) {
    return formatEnum(
      flags,
      ObjectFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatObjectFlags = formatObjectFlags;
  function formatFlowFlags(flags) {
    return formatEnum(
      flags,
      FlowFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatFlowFlags = formatFlowFlags;
  function formatRelationComparisonResult(result) {
    return formatEnum(
      result,
      RelationComparisonResult,
      /*isFlags*/
      true
    );
  }
  Debug2.formatRelationComparisonResult = formatRelationComparisonResult;
  function formatCheckMode(mode) {
    return formatEnum(
      mode,
      CheckMode,
      /*isFlags*/
      true
    );
  }
  Debug2.formatCheckMode = formatCheckMode;
  function formatSignatureCheckMode(mode) {
    return formatEnum(
      mode,
      SignatureCheckMode,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSignatureCheckMode = formatSignatureCheckMode;
  function formatTypeFacts(facts) {
    return formatEnum(
      facts,
      TypeFacts,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTypeFacts = formatTypeFacts;
  let isDebugInfoEnabled = false;
  let flowNodeProto;
  function attachFlowNodeDebugInfoWorker(flowNode) {
    if (!("__debugFlowFlags" in flowNode)) {
      Object.defineProperties(flowNode, {
        // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
        __tsDebuggerDisplay: {
          value() {
            const flowHeader = this.flags & 2 /* Start */ ? "FlowStart" : this.flags & 4 /* BranchLabel */ ? "FlowBranchLabel" : this.flags & 8 /* LoopLabel */ ? "FlowLoopLabel" : this.flags & 16 /* Assignment */ ? "FlowAssignment" : this.flags & 32 /* TrueCondition */ ? "FlowTrueCondition" : this.flags & 64 /* FalseCondition */ ? "FlowFalseCondition" : this.flags & 128 /* SwitchClause */ ? "FlowSwitchClause" : this.flags & 256 /* ArrayMutation */ ? "FlowArrayMutation" : this.flags & 512 /* Call */ ? "FlowCall" : this.flags & 1024 /* ReduceLabel */ ? "FlowReduceLabel" : this.flags & 1 /* Unreachable */ ? "FlowUnreachable" : "UnknownFlow";
            const remainingFlags = this.flags & ~(2048 /* Referenced */ - 1);
            return `${flowHeader}${remainingFlags ? ` (${formatFlowFlags(remainingFlags)})` : ""}`;
          }
        },
        __debugFlowFlags: {
          get() {
            return formatEnum(
              this.flags,
              FlowFlags,
              /*isFlags*/
              true
            );
          }
        },
        __debugToString: {
          value() {
            return formatControlFlowGraph(this);
          }
        }
      });
    }
  }
  function attachFlowNodeDebugInfo(flowNode) {
    if (isDebugInfoEnabled) {
      if (typeof Object.setPrototypeOf === "function") {
        if (!flowNodeProto) {
          flowNodeProto = Object.create(Object.prototype);
          attachFlowNodeDebugInfoWorker(flowNodeProto);
        }
        Object.setPrototypeOf(flowNode, flowNodeProto);
      } else {
        attachFlowNodeDebugInfoWorker(flowNode);
      }
    }
    return flowNode;
  }
  Debug2.attachFlowNodeDebugInfo = attachFlowNodeDebugInfo;
  let nodeArrayProto;
  function attachNodeArrayDebugInfoWorker(array) {
    if (!("__tsDebuggerDisplay" in array)) {
      Object.defineProperties(array, {
        __tsDebuggerDisplay: {
          value(defaultValue) {
            defaultValue = String(defaultValue).replace(/(?:,[\s\w]+:[^,]+)+\]$/, "]");
            return `NodeArray ${defaultValue}`;
          }
        }
      });
    }
  }
  function attachNodeArrayDebugInfo(array) {
    if (isDebugInfoEnabled) {
      if (typeof Object.setPrototypeOf === "function") {
        if (!nodeArrayProto) {
          nodeArrayProto = Object.create(Array.prototype);
          attachNodeArrayDebugInfoWorker(nodeArrayProto);
        }
        Object.setPrototypeOf(array, nodeArrayProto);
      } else {
        attachNodeArrayDebugInfoWorker(array);
      }
    }
  }
  Debug2.attachNodeArrayDebugInfo = attachNodeArrayDebugInfo;
  function enableDebugInfo() {
    if (isDebugInfoEnabled) return;
    const weakTypeTextMap = /* @__PURE__ */ new WeakMap();
    const weakNodeTextMap = /* @__PURE__ */ new WeakMap();
    Object.defineProperties(objectAllocator.getSymbolConstructor().prototype, {
      // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
      __tsDebuggerDisplay: {
        value() {
          const symbolHeader = this.flags & 33554432 /* Transient */ ? "TransientSymbol" : "Symbol";
          const remainingSymbolFlags = this.flags & ~33554432 /* Transient */;
          return `${symbolHeader} '${symbolName(this)}'${remainingSymbolFlags ? ` (${formatSymbolFlags(remainingSymbolFlags)})` : ""}`;
        }
      },
      __debugFlags: {
        get() {
          return formatSymbolFlags(this.flags);
        }
      }
    });
    Object.defineProperties(objectAllocator.getTypeConstructor().prototype, {
      // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
      __tsDebuggerDisplay: {
        value() {
          const typeHeader = this.flags & 67359327 /* Intrinsic */ ? `IntrinsicType ${this.intrinsicName}${this.debugIntrinsicName ? ` (${this.debugIntrinsicName})` : ""}` : this.flags & 98304 /* Nullable */ ? "NullableType" : this.flags & 384 /* StringOrNumberLiteral */ ? `LiteralType ${JSON.stringify(this.value)}` : this.flags & 2048 /* BigIntLiteral */ ? `LiteralType ${this.value.negative ? "-" : ""}${this.value.base10Value}n` : this.flags & 8192 /* UniqueESSymbol */ ? "UniqueESSymbolType" : this.flags & 32 /* Enum */ ? "EnumType" : this.flags & 1048576 /* Union */ ? "UnionType" : this.flags & 2097152 /* Intersection */ ? "IntersectionType" : this.flags & 4194304 /* Index */ ? "IndexType" : this.flags & 8388608 /* IndexedAccess */ ? "IndexedAccessType" : this.flags & 16777216 /* Conditional */ ? "ConditionalType" : this.flags & 33554432 /* Substitution */ ? "SubstitutionType" : this.flags & 262144 /* TypeParameter */ ? "TypeParameter" : this.flags & 524288 /* Object */ ? this.objectFlags & 3 /* ClassOrInterface */ ? "InterfaceType" : this.objectFlags & 4 /* Reference */ ? "TypeReference" : this.objectFlags & 8 /* Tuple */ ? "TupleType" : this.objectFlags & 16 /* Anonymous */ ? "AnonymousType" : this.objectFlags & 32 /* Mapped */ ? "MappedType" : this.objectFlags & 1024 /* ReverseMapped */ ? "ReverseMappedType" : this.objectFlags & 256 /* EvolvingArray */ ? "EvolvingArrayType" : "ObjectType" : "Type";
          const remainingObjectFlags = this.flags & 524288 /* Object */ ? this.objectFlags & ~1343 /* ObjectTypeKindMask */ : 0;
          return `${typeHeader}${this.symbol ? ` '${symbolName(this.symbol)}'` : ""}${remainingObjectFlags ? ` (${formatObjectFlags(remainingObjectFlags)})` : ""}`;
        }
      },
      __debugFlags: {
        get() {
          return formatTypeFlags(this.flags);
        }
      },
      __debugObjectFlags: {
        get() {
          return this.flags & 524288 /* Object */ ? formatObjectFlags(this.objectFlags) : "";
        }
      },
      __debugTypeToString: {
        value() {
          let text = weakTypeTextMap.get(this);
          if (text === void 0) {
            text = this.checker.typeToString(this);
            weakTypeTextMap.set(this, text);
          }
          return text;
        }
      }
    });
    Object.defineProperties(objectAllocator.getSignatureConstructor().prototype, {
      __debugFlags: {
        get() {
          return formatSignatureFlags(this.flags);
        }
      },
      __debugSignatureToString: {
        value() {
          var _a;
          return (_a = this.checker) == null ? void 0 : _a.signatureToString(this);
        }
      }
    });
    const nodeConstructors = [
      objectAllocator.getNodeConstructor(),
      objectAllocator.getIdentifierConstructor(),
      objectAllocator.getTokenConstructor(),
      objectAllocator.getSourceFileConstructor()
    ];
    for (const ctor of nodeConstructors) {
      if (!hasProperty(ctor.prototype, "__debugKind")) {
        Object.defineProperties(ctor.prototype, {
          // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
          __tsDebuggerDisplay: {
            value() {
              const nodeHeader = isGeneratedIdentifier(this) ? "GeneratedIdentifier" : isIdentifier(this) ? `Identifier '${idText(this)}'` : isPrivateIdentifier(this) ? `PrivateIdentifier '${idText(this)}'` : isStringLiteral(this) ? `StringLiteral ${JSON.stringify(this.text.length < 10 ? this.text : this.text.slice(10) + "...")}` : isNumericLiteral(this) ? `NumericLiteral ${this.text}` : isBigIntLiteral(this) ? `BigIntLiteral ${this.text}n` : isTypeParameterDeclaration(this) ? "TypeParameterDeclaration" : isParameter(this) ? "ParameterDeclaration" : isConstructorDeclaration(this) ? "ConstructorDeclaration" : isGetAccessorDeclaration(this) ? "GetAccessorDeclaration" : isSetAccessorDeclaration(this) ? "SetAccessorDeclaration" : isCallSignatureDeclaration(this) ? "CallSignatureDeclaration" : isConstructSignatureDeclaration(this) ? "ConstructSignatureDeclaration" : isIndexSignatureDeclaration(this) ? "IndexSignatureDeclaration" : isTypePredicateNode(this) ? "TypePredicateNode" : isTypeReferenceNode(this) ? "TypeReferenceNode" : isFunctionTypeNode(this) ? "FunctionTypeNode" : isConstructorTypeNode(this) ? "ConstructorTypeNode" : isTypeQueryNode(this) ? "TypeQueryNode" : isTypeLiteralNode(this) ? "TypeLiteralNode" : isArrayTypeNode(this) ? "ArrayTypeNode" : isTupleTypeNode(this) ? "TupleTypeNode" : isOptionalTypeNode(this) ? "OptionalTypeNode" : isRestTypeNode(this) ? "RestTypeNode" : isUnionTypeNode(this) ? "UnionTypeNode" : isIntersectionTypeNode(this) ? "IntersectionTypeNode" : isConditionalTypeNode(this) ? "ConditionalTypeNode" : isInferTypeNode(this) ? "InferTypeNode" : isParenthesizedTypeNode(this) ? "ParenthesizedTypeNode" : isThisTypeNode(this) ? "ThisTypeNode" : isTypeOperatorNode(this) ? "TypeOperatorNode" : isIndexedAccessTypeNode(this) ? "IndexedAccessTypeNode" : isMappedTypeNode(this) ? "MappedTypeNode" : isLiteralTypeNode(this) ? "LiteralTypeNode" : isNamedTupleMember(this) ? "NamedTupleMember" : isImportTypeNode(this) ? "ImportTypeNode" : formatSyntaxKind(this.kind);
              return `${nodeHeader}${this.flags ? ` (${formatNodeFlags(this.flags)})` : ""}`;
            }
          },
          __debugKind: {
            get() {
              return formatSyntaxKind(this.kind);
            }
          },
          __debugNodeFlags: {
            get() {
              return formatNodeFlags(this.flags);
            }
          },
          __debugModifierFlags: {
            get() {
              return formatModifierFlags(getEffectiveModifierFlagsNoCache(this));
            }
          },
          __debugTransformFlags: {
            get() {
              return formatTransformFlags(this.transformFlags);
            }
          },
          __debugIsParseTreeNode: {
            get() {
              return isParseTreeNode(this);
            }
          },
          __debugEmitFlags: {
            get() {
              return formatEmitFlags(getEmitFlags(this));
            }
          },
          __debugGetText: {
            value(includeTrivia) {
              if (nodeIsSynthesized(this)) return "";
              let text = weakNodeTextMap.get(this);
              if (text === void 0) {
                const parseNode = getParseTreeNode(this);
                const sourceFile = parseNode && getSourceFileOfNode(parseNode);
                text = sourceFile ? getSourceTextOfNodeFromSourceFile(sourceFile, parseNode, includeTrivia) : "";
                weakNodeTextMap.set(this, text);
              }
              return text;
            }
          }
        });
      }
    }
    isDebugInfoEnabled = true;
  }
  Debug2.enableDebugInfo = enableDebugInfo;
  function formatVariance(varianceFlags) {
    const variance = varianceFlags & 7 /* VarianceMask */;
    let result = variance === 0 /* Invariant */ ? "in out" : variance === 3 /* Bivariant */ ? "[bivariant]" : variance === 2 /* Contravariant */ ? "in" : variance === 1 /* Covariant */ ? "out" : variance === 4 /* Independent */ ? "[independent]" : "";
    if (varianceFlags & 8 /* Unmeasurable */) {
      result += " (unmeasurable)";
    } else if (varianceFlags & 16 /* Unreliable */) {
      result += " (unreliable)";
    }
    return result;
  }
  Debug2.formatVariance = formatVariance;
  class DebugTypeMapper {
    __debugToString() {
      var _a;
      type(this);
      switch (this.kind) {
        case 3 /* Function */:
          return ((_a = this.debugInfo) == null ? void 0 : _a.call(this)) || "(function mapper)";
        case 0 /* Simple */:
          return `${this.source.__debugTypeToString()} -> ${this.target.__debugTypeToString()}`;
        case 1 /* Array */:
          return zipWith(
            this.sources,
            this.targets || map(this.sources, () => "any"),
            (s, t) => `${s.__debugTypeToString()} -> ${typeof t === "string" ? t : t.__debugTypeToString()}`
          ).join(", ");
        case 2 /* Deferred */:
          return zipWith(
            this.sources,
            this.targets,
            (s, t) => `${s.__debugTypeToString()} -> ${t().__debugTypeToString()}`
          ).join(", ");
        case 5 /* Merged */:
        case 4 /* Composite */:
          return `m1: ${this.mapper1.__debugToString().split("\n").join("\n    ")}
m2: ${this.mapper2.__debugToString().split("\n").join("\n    ")}`;
        default:
          return assertNever(this);
      }
    }
  }
  Debug2.DebugTypeMapper = DebugTypeMapper;
  function attachDebugPrototypeIfDebug(mapper) {
    if (Debug2.isDebugging) {
      return Object.setPrototypeOf(mapper, DebugTypeMapper.prototype);
    }
    return mapper;
  }
  Debug2.attachDebugPrototypeIfDebug = attachDebugPrototypeIfDebug;
  function printControlFlowGraph(flowNode) {
    return console.log(formatControlFlowGraph(flowNode));
  }
  Debug2.printControlFlowGraph = printControlFlowGraph;
  function formatControlFlowGraph(flowNode) {
    let nextDebugFlowId = -1;
    function getDebugFlowNodeId(f) {
      if (!f.id) {
        f.id = nextDebugFlowId;
        nextDebugFlowId--;
      }
      return f.id;
    }
    let BoxCharacter;
    ((BoxCharacter2) => {
      BoxCharacter2["lr"] = "\u2500";
      BoxCharacter2["ud"] = "\u2502";
      BoxCharacter2["dr"] = "\u256D";
      BoxCharacter2["dl"] = "\u256E";
      BoxCharacter2["ul"] = "\u256F";
      BoxCharacter2["ur"] = "\u2570";
      BoxCharacter2["udr"] = "\u251C";
      BoxCharacter2["udl"] = "\u2524";
      BoxCharacter2["dlr"] = "\u252C";
      BoxCharacter2["ulr"] = "\u2534";
      BoxCharacter2["udlr"] = "\u256B";
    })(BoxCharacter || (BoxCharacter = {}));
    let Connection;
    ((Connection2) => {
      Connection2[Connection2["None"] = 0] = "None";
      Connection2[Connection2["Up"] = 1] = "Up";
      Connection2[Connection2["Down"] = 2] = "Down";
      Connection2[Connection2["Left"] = 4] = "Left";
      Connection2[Connection2["Right"] = 8] = "Right";
      Connection2[Connection2["UpDown"] = 3] = "UpDown";
      Connection2[Connection2["LeftRight"] = 12] = "LeftRight";
      Connection2[Connection2["UpLeft"] = 5] = "UpLeft";
      Connection2[Connection2["UpRight"] = 9] = "UpRight";
      Connection2[Connection2["DownLeft"] = 6] = "DownLeft";
      Connection2[Connection2["DownRight"] = 10] = "DownRight";
      Connection2[Connection2["UpDownLeft"] = 7] = "UpDownLeft";
      Connection2[Connection2["UpDownRight"] = 11] = "UpDownRight";
      Connection2[Connection2["UpLeftRight"] = 13] = "UpLeftRight";
      Connection2[Connection2["DownLeftRight"] = 14] = "DownLeftRight";
      Connection2[Connection2["UpDownLeftRight"] = 15] = "UpDownLeftRight";
      Connection2[Connection2["NoChildren"] = 16] = "NoChildren";
    })(Connection || (Connection = {}));
    const hasAntecedentFlags = 16 /* Assignment */ | 96 /* Condition */ | 128 /* SwitchClause */ | 256 /* ArrayMutation */ | 512 /* Call */ | 1024 /* ReduceLabel */;
    const hasNodeFlags = 2 /* Start */ | 16 /* Assignment */ | 512 /* Call */ | 96 /* Condition */ | 256 /* ArrayMutation */;
    const links = /* @__PURE__ */ Object.create(
      /*o*/
      null
    );
    const nodes = [];
    const edges = [];
    const root = buildGraphNode(flowNode, /* @__PURE__ */ new Set());
    for (const node of nodes) {
      node.text = renderFlowNode(node.flowNode, node.circular);
      computeLevel(node);
    }
    const height = computeHeight(root);
    const columnWidths = computeColumnWidths(height);
    computeLanes(root, 0);
    return renderGraph();
    function isFlowSwitchClause(f) {
      return !!(f.flags & 128 /* SwitchClause */);
    }
    function hasAntecedents(f) {
      return !!(f.flags & 12 /* Label */) && !!f.antecedent;
    }
    function hasAntecedent(f) {
      return !!(f.flags & hasAntecedentFlags);
    }
    function hasNode(f) {
      return !!(f.flags & hasNodeFlags);
    }
    function getChildren(node) {
      const children = [];
      for (const edge of node.edges) {
        if (edge.source === node) {
          children.push(edge.target);
        }
      }
      return children;
    }
    function getParents(node) {
      const parents = [];
      for (const edge of node.edges) {
        if (edge.target === node) {
          parents.push(edge.source);
        }
      }
      return parents;
    }
    function buildGraphNode(flowNode2, seen) {
      const id = getDebugFlowNodeId(flowNode2);
      let graphNode = links[id];
      if (graphNode && seen.has(flowNode2)) {
        graphNode.circular = true;
        graphNode = {
          id: -1,
          flowNode: flowNode2,
          edges: [],
          text: "",
          lane: -1,
          endLane: -1,
          level: -1,
          circular: "circularity"
        };
        nodes.push(graphNode);
        return graphNode;
      }
      seen.add(flowNode2);
      if (!graphNode) {
        links[id] = graphNode = { id, flowNode: flowNode2, edges: [], text: "", lane: -1, endLane: -1, level: -1, circular: false };
        nodes.push(graphNode);
        if (hasAntecedents(flowNode2)) {
          for (const antecedent of flowNode2.antecedent) {
            buildGraphEdge(graphNode, antecedent, seen);
          }
        } else if (hasAntecedent(flowNode2)) {
          buildGraphEdge(graphNode, flowNode2.antecedent, seen);
        }
      }
      seen.delete(flowNode2);
      return graphNode;
    }
    function buildGraphEdge(source, antecedent, seen) {
      const target = buildGraphNode(antecedent, seen);
      const edge = { source, target };
      edges.push(edge);
      source.edges.push(edge);
      target.edges.push(edge);
    }
    function computeLevel(node) {
      if (node.level !== -1) {
        return node.level;
      }
      let level = 0;
      for (const parent of getParents(node)) {
        level = Math.max(level, computeLevel(parent) + 1);
      }
      return node.level = level;
    }
    function computeHeight(node) {
      let height2 = 0;
      for (const child of getChildren(node)) {
        height2 = Math.max(height2, computeHeight(child));
      }
      return height2 + 1;
    }
    function computeColumnWidths(height2) {
      const columns = fill(Array(height2), 0);
      for (const node of nodes) {
        columns[node.level] = Math.max(columns[node.level], node.text.length);
      }
      return columns;
    }
    function computeLanes(node, lane) {
      if (node.lane === -1) {
        node.lane = lane;
        node.endLane = lane;
        const children = getChildren(node);
        for (let i = 0; i < children.length; i++) {
          if (i > 0) lane++;
          const child = children[i];
          computeLanes(child, lane);
          if (child.endLane > node.endLane) {
            lane = child.endLane;
          }
        }
        node.endLane = lane;
      }
    }
    function getHeader2(flags) {
      if (flags & 2 /* Start */) return "Start";
      if (flags & 4 /* BranchLabel */) return "Branch";
      if (flags & 8 /* LoopLabel */) return "Loop";
      if (flags & 16 /* Assignment */) return "Assignment";
      if (flags & 32 /* TrueCondition */) return "True";
      if (flags & 64 /* FalseCondition */) return "False";
      if (flags & 128 /* SwitchClause */) return "SwitchClause";
      if (flags & 256 /* ArrayMutation */) return "ArrayMutation";
      if (flags & 512 /* Call */) return "Call";
      if (flags & 1024 /* ReduceLabel */) return "ReduceLabel";
      if (flags & 1 /* Unreachable */) return "Unreachable";
      throw new Error();
    }
    function getNodeText(node) {
      const sourceFile = getSourceFileOfNode(node);
      return getSourceTextOfNodeFromSourceFile(
        sourceFile,
        node,
        /*includeTrivia*/
        false
      );
    }
    function renderFlowNode(flowNode2, circular) {
      let text = getHeader2(flowNode2.flags);
      if (circular) {
        text = `${text}#${getDebugFlowNodeId(flowNode2)}`;
      }
      if (isFlowSwitchClause(flowNode2)) {
        const clauses = [];
        const { switchStatement, clauseStart, clauseEnd } = flowNode2.node;
        for (let i = clauseStart; i < clauseEnd; i++) {
          const clause = switchStatement.caseBlock.clauses[i];
          if (isDefaultClause(clause)) {
            clauses.push("default");
          } else {
            clauses.push(getNodeText(clause.expression));
          }
        }
        text += ` (${clauses.join(", ")})`;
      } else if (hasNode(flowNode2)) {
        if (flowNode2.node) {
          text += ` (${getNodeText(flowNode2.node)})`;
        }
      }
      return circular === "circularity" ? `Circular(${text})` : text;
    }
    function renderGraph() {
      const columnCount = columnWidths.length;
      const laneCount = maxBy(nodes, 0, (n) => n.lane) + 1;
      const lanes = fill(Array(laneCount), "");
      const grid = columnWidths.map(() => Array(laneCount));
      const connectors = columnWidths.map(() => fill(Array(laneCount), 0));
      for (const node of nodes) {
        grid[node.level][node.lane] = node;
        const children = getChildren(node);
        for (let i = 0; i < children.length; i++) {
          const child = children[i];
          let connector = 8 /* Right */;
          if (child.lane === node.lane) connector |= 4 /* Left */;
          if (i > 0) connector |= 1 /* Up */;
          if (i < children.length - 1) connector |= 2 /* Down */;
          connectors[node.level][child.lane] |= connector;
        }
        if (children.length === 0) {
          connectors[node.level][node.lane] |= 16 /* NoChildren */;
        }
        const parents = getParents(node);
        for (let i = 0; i < parents.length; i++) {
          const parent = parents[i];
          let connector = 4 /* Left */;
          if (i > 0) connector |= 1 /* Up */;
          if (i < parents.length - 1) connector |= 2 /* Down */;
          connectors[node.level - 1][parent.lane] |= connector;
        }
      }
      for (let column = 0; column < columnCount; column++) {
        for (let lane = 0; lane < laneCount; lane++) {
          const left = column > 0 ? connectors[column - 1][lane] : 0;
          const above = lane > 0 ? connectors[column][lane - 1] : 0;
          let connector = connectors[column][lane];
          if (!connector) {
            if (left & 8 /* Right */) connector |= 12 /* LeftRight */;
            if (above & 2 /* Down */) connector |= 3 /* UpDown */;
            connectors[column][lane] = connector;
          }
        }
      }
      for (let column = 0; column < columnCount; column++) {
        for (let lane = 0; lane < lanes.length; lane++) {
          const connector = connectors[column][lane];
          const fill2 = connector & 4 /* Left */ ? "\u2500" /* lr */ : " ";
          const node = grid[column][lane];
          if (!node) {
            if (column < columnCount - 1) {
              writeLane(lane, repeat(fill2, columnWidths[column] + 1));
            }
          } else {
            writeLane(lane, node.text);
            if (column < columnCount - 1) {
              writeLane(lane, " ");
              writeLane(lane, repeat(fill2, columnWidths[column] - node.text.length));
            }
          }
          writeLane(lane, getBoxCharacter(connector));
          writeLane(lane, connector & 8 /* Right */ && column < columnCount - 1 && !grid[column + 1][lane] ? "\u2500" /* lr */ : " ");
        }
      }
      return `
${lanes.join("\n")}
`;
      function writeLane(lane, text) {
        lanes[lane] += text;
      }
    }
    function getBoxCharacter(connector) {
      switch (connector) {
        case 3 /* UpDown */:
          return "\u2502" /* ud */;
        case 12 /* LeftRight */:
          return "\u2500" /* lr */;
        case 5 /* UpLeft */:
          return "\u256F" /* ul */;
        case 9 /* UpRight */:
          return "\u2570" /* ur */;
        case 6 /* DownLeft */:
          return "\u256E" /* dl */;
        case 10 /* DownRight */:
          return "\u256D" /* dr */;
        case 7 /* UpDownLeft */:
          return "\u2524" /* udl */;
        case 11 /* UpDownRight */:
          return "\u251C" /* udr */;
        case 13 /* UpLeftRight */:
          return "\u2534" /* ulr */;
        case 14 /* DownLeftRight */:
          return "\u252C" /* dlr */;
        case 15 /* UpDownLeftRight */:
          return "\u256B" /* udlr */;
      }
      return " ";
    }
    function fill(array, value) {
      if (array.fill) {
        array.fill(value);
      } else {
        for (let i = 0; i < array.length; i++) {
          array[i] = value;
        }
      }
      return array;
    }
    function repeat(ch, length2) {
      if (ch.repeat) {
        return length2 > 0 ? ch.repeat(length2) : "";
      }
      let s = "";
      while (s.length < length2) {
        s += ch;
      }
      return s;
    }
  }
  Debug2.formatControlFlowGraph = formatControlFlowGraph;
})(Debug || (Debug = {}));

// src/compiler/semver.ts
var versionRegExp = /^(0|[1-9]\d*)(?:\.(0|[1-9]\d*)(?:\.(0|[1-9]\d*)(?:-([a-z0-9-.]+))?(?:\+([a-z0-9-.]+))?)?)?$/i;
var prereleaseRegExp = /^(?:0|[1-9]\d*|[a-z-][a-z0-9-]*)(?:\.(?:0|[1-9]\d*|[a-z-][a-z0-9-]*))*$/i;
var prereleasePartRegExp = /^(?:0|[1-9]\d*|[a-z-][a-z0-9-]*)$/i;
var buildRegExp = /^[a-z0-9-]+(?:\.[a-z0-9-]+)*$/i;
var buildPartRegExp = /^[a-z0-9-]+$/i;
var numericIdentifierRegExp = /^(?:0|[1-9]\d*)$/;
var _Version = class _Version {
  constructor(major, minor = 0, patch = 0, prerelease = "", build2 = "") {
    if (typeof major === "string") {
      const result = Debug.checkDefined(tryParseComponents(major), "Invalid version");
      ({ major, minor, patch, prerelease, build: build2 } = result);
    }
    Debug.assert(major >= 0, "Invalid argument: major");
    Debug.assert(minor >= 0, "Invalid argument: minor");
    Debug.assert(patch >= 0, "Invalid argument: patch");
    const prereleaseArray = prerelease ? isArray(prerelease) ? prerelease : prerelease.split(".") : emptyArray;
    const buildArray = build2 ? isArray(build2) ? build2 : build2.split(".") : emptyArray;
    Debug.assert(every(prereleaseArray, (s) => prereleasePartRegExp.test(s)), "Invalid argument: prerelease");
    Debug.assert(every(buildArray, (s) => buildPartRegExp.test(s)), "Invalid argument: build");
    this.major = major;
    this.minor = minor;
    this.patch = patch;
    this.prerelease = prereleaseArray;
    this.build = buildArray;
  }
  static tryParse(text) {
    const result = tryParseComponents(text);
    if (!result) return void 0;
    const { major, minor, patch, prerelease, build: build2 } = result;
    return new _Version(major, minor, patch, prerelease, build2);
  }
  compareTo(other) {
    if (this === other) return 0 /* EqualTo */;
    if (other === void 0) return 1 /* GreaterThan */;
    return compareValues(this.major, other.major) || compareValues(this.minor, other.minor) || compareValues(this.patch, other.patch) || comparePrereleaseIdentifiers(this.prerelease, other.prerelease);
  }
  increment(field) {
    switch (field) {
      case "major":
        return new _Version(this.major + 1, 0, 0);
      case "minor":
        return new _Version(this.major, this.minor + 1, 0);
      case "patch":
        return new _Version(this.major, this.minor, this.patch + 1);
      default:
        return Debug.assertNever(field);
    }
  }
  with(fields) {
    const {
      major = this.major,
      minor = this.minor,
      patch = this.patch,
      prerelease = this.prerelease,
      build: build2 = this.build
    } = fields;
    return new _Version(major, minor, patch, prerelease, build2);
  }
  toString() {
    let result = `${this.major}.${this.minor}.${this.patch}`;
    if (some(this.prerelease)) result += `-${this.prerelease.join(".")}`;
    if (some(this.build)) result += `+${this.build.join(".")}`;
    return result;
  }
};
_Version.zero = new _Version(0, 0, 0, ["0"]);
var Version = _Version;
function tryParseComponents(text) {
  const match = versionRegExp.exec(text);
  if (!match) return void 0;
  const [, major, minor = "0", patch = "0", prerelease = "", build2 = ""] = match;
  if (prerelease && !prereleaseRegExp.test(prerelease)) return void 0;
  if (build2 && !buildRegExp.test(build2)) return void 0;
  return {
    major: parseInt(major, 10),
    minor: parseInt(minor, 10),
    patch: parseInt(patch, 10),
    prerelease,
    build: build2
  };
}
function comparePrereleaseIdentifiers(left, right) {
  if (left === right) return 0 /* EqualTo */;
  if (left.length === 0) return right.length === 0 ? 0 /* EqualTo */ : 1 /* GreaterThan */;
  if (right.length === 0) return -1 /* LessThan */;
  const length2 = Math.min(left.length, right.length);
  for (let i = 0; i < length2; i++) {
    const leftIdentifier = left[i];
    const rightIdentifier = right[i];
    if (leftIdentifier === rightIdentifier) continue;
    const leftIsNumeric = numericIdentifierRegExp.test(leftIdentifier);
    const rightIsNumeric = numericIdentifierRegExp.test(rightIdentifier);
    if (leftIsNumeric || rightIsNumeric) {
      if (leftIsNumeric !== rightIsNumeric) return leftIsNumeric ? -1 /* LessThan */ : 1 /* GreaterThan */;
      const result = compareValues(+leftIdentifier, +rightIdentifier);
      if (result) return result;
    } else {
      const result = compareStringsCaseSensitive(leftIdentifier, rightIdentifier);
      if (result) return result;
    }
  }
  return compareValues(left.length, right.length);
}
var VersionRange = class _VersionRange {
  constructor(spec) {
    this._alternatives = spec ? Debug.checkDefined(parseRange(spec), "Invalid range spec.") : emptyArray;
  }
  static tryParse(text) {
    const sets = parseRange(text);
    if (sets) {
      const range = new _VersionRange("");
      range._alternatives = sets;
      return range;
    }
    return void 0;
  }
  /**
   * Tests whether a version matches the range. This is equivalent to `satisfies(version, range, { includePrerelease: true })`.
   * in `node-semver`.
   */
  test(version2) {
    if (typeof version2 === "string") version2 = new Version(version2);
    return testDisjunction(version2, this._alternatives);
  }
  toString() {
    return formatDisjunction(this._alternatives);
  }
};
var logicalOrRegExp = /\|\|/;
var whitespaceRegExp = /\s+/;
var partialRegExp = /^([x*0]|[1-9]\d*)(?:\.([x*0]|[1-9]\d*)(?:\.([x*0]|[1-9]\d*)(?:-([a-z0-9-.]+))?(?:\+([a-z0-9-.]+))?)?)?$/i;
var hyphenRegExp = /^\s*([a-z0-9-+.*]+)\s+-\s+([a-z0-9-+.*]+)\s*$/i;
var rangeRegExp = /^([~^<>=]|<=|>=)?\s*([a-z0-9-+.*]+)$/i;
function parseRange(text) {
  const alternatives = [];
  for (let range of text.trim().split(logicalOrRegExp)) {
    if (!range) continue;
    const comparators = [];
    range = range.trim();
    const match = hyphenRegExp.exec(range);
    if (match) {
      if (!parseHyphen(match[1], match[2], comparators)) return void 0;
    } else {
      for (const simple of range.split(whitespaceRegExp)) {
        const match2 = rangeRegExp.exec(simple.trim());
        if (!match2 || !parseComparator(match2[1], match2[2], comparators)) return void 0;
      }
    }
    alternatives.push(comparators);
  }
  return alternatives;
}
function parsePartial(text) {
  const match = partialRegExp.exec(text);
  if (!match) return void 0;
  const [, major, minor = "*", patch = "*", prerelease, build2] = match;
  const version2 = new Version(
    isWildcard(major) ? 0 : parseInt(major, 10),
    isWildcard(major) || isWildcard(minor) ? 0 : parseInt(minor, 10),
    isWildcard(major) || isWildcard(minor) || isWildcard(patch) ? 0 : parseInt(patch, 10),
    prerelease,
    build2
  );
  return { version: version2, major, minor, patch };
}
function parseHyphen(left, right, comparators) {
  const leftResult = parsePartial(left);
  if (!leftResult) return false;
  const rightResult = parsePartial(right);
  if (!rightResult) return false;
  if (!isWildcard(leftResult.major)) {
    comparators.push(createComparator(">=", leftResult.version));
  }
  if (!isWildcard(rightResult.major)) {
    comparators.push(
      isWildcard(rightResult.minor) ? createComparator("<", rightResult.version.increment("major")) : isWildcard(rightResult.patch) ? createComparator("<", rightResult.version.increment("minor")) : createComparator("<=", rightResult.version)
    );
  }
  return true;
}
function parseComparator(operator, text, comparators) {
  const result = parsePartial(text);
  if (!result) return false;
  const { version: version2, major, minor, patch } = result;
  if (!isWildcard(major)) {
    switch (operator) {
      case "~":
        comparators.push(createComparator(">=", version2));
        comparators.push(createComparator(
          "<",
          version2.increment(
            isWildcard(minor) ? "major" : "minor"
          )
        ));
        break;
      case "^":
        comparators.push(createComparator(">=", version2));
        comparators.push(createComparator(
          "<",
          version2.increment(
            version2.major > 0 || isWildcard(minor) ? "major" : version2.minor > 0 || isWildcard(patch) ? "minor" : "patch"
          )
        ));
        break;
      case "<":
      case ">=":
        comparators.push(
          isWildcard(minor) || isWildcard(patch) ? createComparator(operator, version2.with({ prerelease: "0" })) : createComparator(operator, version2)
        );
        break;
      case "<=":
      case ">":
        comparators.push(
          isWildcard(minor) ? createComparator(operator === "<=" ? "<" : ">=", version2.increment("major").with({ prerelease: "0" })) : isWildcard(patch) ? createComparator(operator === "<=" ? "<" : ">=", version2.increment("minor").with({ prerelease: "0" })) : createComparator(operator, version2)
        );
        break;
      case "=":
      case void 0:
        if (isWildcard(minor) || isWildcard(patch)) {
          comparators.push(createComparator(">=", version2.with({ prerelease: "0" })));
          comparators.push(createComparator("<", version2.increment(isWildcard(minor) ? "major" : "minor").with({ prerelease: "0" })));
        } else {
          comparators.push(createComparator("=", version2));
        }
        break;
      default:
        return false;
    }
  } else if (operator === "<" || operator === ">") {
    comparators.push(createComparator("<", Version.zero));
  }
  return true;
}
function isWildcard(part) {
  return part === "*" || part === "x" || part === "X";
}
function createComparator(operator, operand) {
  return { operator, operand };
}
function testDisjunction(version2, alternatives) {
  if (alternatives.length === 0) return true;
  for (const alternative of alternatives) {
    if (testAlternative(version2, alternative)) return true;
  }
  return false;
}
function testAlternative(version2, comparators) {
  for (const comparator of comparators) {
    if (!testComparator(version2, comparator.operator, comparator.operand)) return false;
  }
  return true;
}
function testComparator(version2, operator, operand) {
  const cmp = version2.compareTo(operand);
  switch (operator) {
    case "<":
      return cmp < 0;
    case "<=":
      return cmp <= 0;
    case ">":
      return cmp > 0;
    case ">=":
      return cmp >= 0;
    case "=":
      return cmp === 0;
    default:
      return Debug.assertNever(operator);
  }
}
function formatDisjunction(alternatives) {
  return map(alternatives, formatAlternative).join(" || ") || "*";
}
function formatAlternative(comparators) {
  return map(comparators, formatComparator).join(" ");
}
function formatComparator(comparator) {
  return `${comparator.operator}${comparator.operand}`;
}

// src/compiler/performanceCore.ts
function tryGetPerformance() {
  if (isNodeLikeSystem()) {
    try {
      const { performance: performance2 } = require("perf_hooks");
      if (performance2) {
        return {
          shouldWriteNativeEvents: false,
          performance: performance2
        };
      }
    } catch {
    }
  }
  if (typeof performance === "object") {
    return {
      shouldWriteNativeEvents: true,
      performance
    };
  }
  return void 0;
}
function tryGetPerformanceHooks() {
  const p = tryGetPerformance();
  if (!p) return void 0;
  const { shouldWriteNativeEvents, performance: performance2 } = p;
  const hooks = {
    shouldWriteNativeEvents,
    performance: void 0,
    performanceTime: void 0
  };
  if (typeof performance2.timeOrigin === "number" && typeof performance2.now === "function") {
    hooks.performanceTime = performance2;
  }
  if (hooks.performanceTime && typeof performance2.mark === "function" && typeof performance2.measure === "function" && typeof performance2.clearMarks === "function" && typeof performance2.clearMeasures === "function") {
    hooks.performance = performance2;
  }
  return hooks;
}
var nativePerformanceHooks = tryGetPerformanceHooks();
var nativePerformanceTime = nativePerformanceHooks == null ? void 0 : nativePerformanceHooks.performanceTime;
function tryGetNativePerformanceHooks() {
  return nativePerformanceHooks;
}
var timestamp = nativePerformanceTime ? () => nativePerformanceTime.now() : Date.now;

// src/compiler/performance.ts
var perfHooks;
var performanceImpl;
function createTimerIf(condition, measureName, startMarkName, endMarkName) {
  return condition ? createTimer(measureName, startMarkName, endMarkName) : nullTimer;
}
function createTimer(measureName, startMarkName, endMarkName) {
  let enterCount = 0;
  return {
    enter,
    exit
  };
  function enter() {
    if (++enterCount === 1) {
      mark(startMarkName);
    }
  }
  function exit() {
    if (--enterCount === 0) {
      mark(endMarkName);
      measure(measureName, startMarkName, endMarkName);
    } else if (enterCount < 0) {
      Debug.fail("enter/exit count does not match.");
    }
  }
}
var nullTimer = { enter: noop, exit: noop };
var enabled = false;
var timeorigin = timestamp();
var marks = /* @__PURE__ */ new Map();
var counts = /* @__PURE__ */ new Map();
var durations = /* @__PURE__ */ new Map();
function mark(markName) {
  if (enabled) {
    const count = counts.get(markName) ?? 0;
    counts.set(markName, count + 1);
    marks.set(markName, timestamp());
    performanceImpl == null ? void 0 : performanceImpl.mark(markName);
    if (typeof onProfilerEvent === "function") {
      onProfilerEvent(markName);
    }
  }
}
function measure(measureName, startMarkName, endMarkName) {
  if (enabled) {
    const end = (endMarkName !== void 0 ? marks.get(endMarkName) : void 0) ?? timestamp();
    const start = (startMarkName !== void 0 ? marks.get(startMarkName) : void 0) ?? timeorigin;
    const previousDuration = durations.get(measureName) || 0;
    durations.set(measureName, previousDuration + (end - start));
    performanceImpl == null ? void 0 : performanceImpl.measure(measureName, startMarkName, endMarkName);
  }
}
function getCount(markName) {
  return counts.get(markName) || 0;
}
function getDuration(measureName) {
  return durations.get(measureName) || 0;
}
function forEachMeasure(cb) {
  durations.forEach((duration, measureName) => cb(measureName, duration));
}
function forEachMark(cb) {
  marks.forEach((_time, markName) => cb(markName));
}
function clearMeasures(name) {
  if (name !== void 0) durations.delete(name);
  else durations.clear();
  performanceImpl == null ? void 0 : performanceImpl.clearMeasures(name);
}
function clearMarks(name) {
  if (name !== void 0) {
    counts.delete(name);
    marks.delete(name);
  } else {
    counts.clear();
    marks.clear();
  }
  performanceImpl == null ? void 0 : performanceImpl.clearMarks(name);
}
function isEnabled() {
  return enabled;
}
function enable(system = sys) {
  var _a;
  if (!enabled) {
    enabled = true;
    perfHooks || (perfHooks = tryGetNativePerformanceHooks());
    if (perfHooks == null ? void 0 : perfHooks.performance) {
      timeorigin = perfHooks.performance.timeOrigin;
      if (perfHooks.shouldWriteNativeEvents || ((_a = system == null ? void 0 : system.cpuProfilingEnabled) == null ? void 0 : _a.call(system)) || (system == null ? void 0 : system.debugMode)) {
        performanceImpl = perfHooks.performance;
      }
    }
  }
  return true;
}
function disable() {
  if (enabled) {
    marks.clear();
    counts.clear();
    durations.clear();
    performanceImpl = void 0;
    enabled = false;
  }
}

// src/compiler/tracing.ts
var tracing;
var tracingEnabled;
((tracingEnabled2) => {
  let fs;
  let traceCount = 0;
  let traceFd = 0;
  let mode;
  const typeCatalog = [];
  let legendPath;
  const legend = [];
  function startTracing2(tracingMode, traceDir, configFilePath) {
    Debug.assert(!tracing, "Tracing already started");
    if (fs === void 0) {
      try {
        fs = require("fs");
      } catch (e) {
        throw new Error(`tracing requires having fs
(original error: ${e.message || e})`);
      }
    }
    mode = tracingMode;
    typeCatalog.length = 0;
    if (legendPath === void 0) {
      legendPath = combinePaths(traceDir, "legend.json");
    }
    if (!fs.existsSync(traceDir)) {
      fs.mkdirSync(traceDir, { recursive: true });
    }
    const countPart = mode === "build" ? `.${process.pid}-${++traceCount}` : mode === "server" ? `.${process.pid}` : ``;
    const tracePath = combinePaths(traceDir, `trace${countPart}.json`);
    const typesPath = combinePaths(traceDir, `types${countPart}.json`);
    legend.push({
      configFilePath,
      tracePath,
      typesPath
    });
    traceFd = fs.openSync(tracePath, "w");
    tracing = tracingEnabled2;
    const meta = { cat: "__metadata", ph: "M", ts: 1e3 * timestamp(), pid: 1, tid: 1 };
    fs.writeSync(
      traceFd,
      "[\n" + [{ name: "process_name", args: { name: "tsc" }, ...meta }, { name: "thread_name", args: { name: "Main" }, ...meta }, { name: "TracingStartedInBrowser", ...meta, cat: "disabled-by-default-devtools.timeline" }].map((v) => JSON.stringify(v)).join(",\n")
    );
  }
  tracingEnabled2.startTracing = startTracing2;
  function stopTracing() {
    Debug.assert(tracing, "Tracing is not in progress");
    Debug.assert(!!typeCatalog.length === (mode !== "server"));
    fs.writeSync(traceFd, `
]
`);
    fs.closeSync(traceFd);
    tracing = void 0;
    if (typeCatalog.length) {
      dumpTypes(typeCatalog);
    } else {
      legend[legend.length - 1].typesPath = void 0;
    }
  }
  tracingEnabled2.stopTracing = stopTracing;
  function recordType(type) {
    if (mode !== "server") {
      typeCatalog.push(type);
    }
  }
  tracingEnabled2.recordType = recordType;
  let Phase;
  ((Phase2) => {
    Phase2["Parse"] = "parse";
    Phase2["Program"] = "program";
    Phase2["Bind"] = "bind";
    Phase2["Check"] = "check";
    Phase2["CheckTypes"] = "checkTypes";
    Phase2["Emit"] = "emit";
    Phase2["Session"] = "session";
  })(Phase = tracingEnabled2.Phase || (tracingEnabled2.Phase = {}));
  function instant(phase, name, args) {
    writeEvent("I", phase, name, args, `"s":"g"`);
  }
  tracingEnabled2.instant = instant;
  const eventStack = [];
  function push(phase, name, args, separateBeginAndEnd = false) {
    if (separateBeginAndEnd) {
      writeEvent("B", phase, name, args);
    }
    eventStack.push({ phase, name, args, time: 1e3 * timestamp(), separateBeginAndEnd });
  }
  tracingEnabled2.push = push;
  function pop(results) {
    Debug.assert(eventStack.length > 0);
    writeStackEvent(eventStack.length - 1, 1e3 * timestamp(), results);
    eventStack.length--;
  }
  tracingEnabled2.pop = pop;
  function popAll() {
    const endTime = 1e3 * timestamp();
    for (let i = eventStack.length - 1; i >= 0; i--) {
      writeStackEvent(i, endTime);
    }
    eventStack.length = 0;
  }
  tracingEnabled2.popAll = popAll;
  const sampleInterval = 1e3 * 10;
  function writeStackEvent(index, endTime, results) {
    const { phase, name, args, time, separateBeginAndEnd } = eventStack[index];
    if (separateBeginAndEnd) {
      Debug.assert(!results, "`results` are not supported for events with `separateBeginAndEnd`");
      writeEvent(
        "E",
        phase,
        name,
        args,
        /*extras*/
        void 0,
        endTime
      );
    } else if (sampleInterval - time % sampleInterval <= endTime - time) {
      writeEvent("X", phase, name, { ...args, results }, `"dur":${endTime - time}`, time);
    }
  }
  function writeEvent(eventType, phase, name, args, extras, time = 1e3 * timestamp()) {
    if (mode === "server" && phase === "checkTypes" /* CheckTypes */) return;
    mark("beginTracing");
    fs.writeSync(traceFd, `,
{"pid":1,"tid":1,"ph":"${eventType}","cat":"${phase}","ts":${time},"name":"${name}"`);
    if (extras) fs.writeSync(traceFd, `,${extras}`);
    if (args) fs.writeSync(traceFd, `,"args":${JSON.stringify(args)}`);
    fs.writeSync(traceFd, `}`);
    mark("endTracing");
    measure("Tracing", "beginTracing", "endTracing");
  }
  function getLocation(node) {
    const file = getSourceFileOfNode(node);
    return !file ? void 0 : {
      path: file.path,
      start: indexFromOne(getLineAndCharacterOfPosition(file, node.pos)),
      end: indexFromOne(getLineAndCharacterOfPosition(file, node.end))
    };
    function indexFromOne(lc) {
      return {
        line: lc.line + 1,
        character: lc.character + 1
      };
    }
  }
  function dumpTypes(types) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s;
    mark("beginDumpTypes");
    const typesPath = legend[legend.length - 1].typesPath;
    const typesFd = fs.openSync(typesPath, "w");
    const recursionIdentityMap = /* @__PURE__ */ new Map();
    fs.writeSync(typesFd, "[");
    const numTypes = types.length;
    for (let i = 0; i < numTypes; i++) {
      const type = types[i];
      const objectFlags = type.objectFlags;
      const symbol = type.aliasSymbol ?? type.symbol;
      let display;
      if (objectFlags & 16 /* Anonymous */ | type.flags & 2944 /* Literal */) {
        try {
          display = (_a = type.checker) == null ? void 0 : _a.typeToString(type);
        } catch {
          display = void 0;
        }
      }
      let indexedAccessProperties = {};
      if (type.flags & 8388608 /* IndexedAccess */) {
        const indexedAccessType = type;
        indexedAccessProperties = {
          indexedAccessObjectType: (_b = indexedAccessType.objectType) == null ? void 0 : _b.id,
          indexedAccessIndexType: (_c = indexedAccessType.indexType) == null ? void 0 : _c.id
        };
      }
      let referenceProperties = {};
      if (objectFlags & 4 /* Reference */) {
        const referenceType = type;
        referenceProperties = {
          instantiatedType: (_d = referenceType.target) == null ? void 0 : _d.id,
          typeArguments: (_e = referenceType.resolvedTypeArguments) == null ? void 0 : _e.map((t) => t.id),
          referenceLocation: getLocation(referenceType.node)
        };
      }
      let conditionalProperties = {};
      if (type.flags & 16777216 /* Conditional */) {
        const conditionalType = type;
        conditionalProperties = {
          conditionalCheckType: (_f = conditionalType.checkType) == null ? void 0 : _f.id,
          conditionalExtendsType: (_g = conditionalType.extendsType) == null ? void 0 : _g.id,
          conditionalTrueType: ((_h = conditionalType.resolvedTrueType) == null ? void 0 : _h.id) ?? -1,
          conditionalFalseType: ((_i = conditionalType.resolvedFalseType) == null ? void 0 : _i.id) ?? -1
        };
      }
      let substitutionProperties = {};
      if (type.flags & 33554432 /* Substitution */) {
        const substitutionType = type;
        substitutionProperties = {
          substitutionBaseType: (_j = substitutionType.baseType) == null ? void 0 : _j.id,
          constraintType: (_k = substitutionType.constraint) == null ? void 0 : _k.id
        };
      }
      let reverseMappedProperties = {};
      if (objectFlags & 1024 /* ReverseMapped */) {
        const reverseMappedType = type;
        reverseMappedProperties = {
          reverseMappedSourceType: (_l = reverseMappedType.source) == null ? void 0 : _l.id,
          reverseMappedMappedType: (_m = reverseMappedType.mappedType) == null ? void 0 : _m.id,
          reverseMappedConstraintType: (_n = reverseMappedType.constraintType) == null ? void 0 : _n.id
        };
      }
      let evolvingArrayProperties = {};
      if (objectFlags & 256 /* EvolvingArray */) {
        const evolvingArrayType = type;
        evolvingArrayProperties = {
          evolvingArrayElementType: evolvingArrayType.elementType.id,
          evolvingArrayFinalType: (_o = evolvingArrayType.finalArrayType) == null ? void 0 : _o.id
        };
      }
      let recursionToken;
      const recursionIdentity = type.checker.getRecursionIdentity(type);
      if (recursionIdentity) {
        recursionToken = recursionIdentityMap.get(recursionIdentity);
        if (!recursionToken) {
          recursionToken = recursionIdentityMap.size;
          recursionIdentityMap.set(recursionIdentity, recursionToken);
        }
      }
      const descriptor = {
        id: type.id,
        intrinsicName: type.intrinsicName,
        symbolName: (symbol == null ? void 0 : symbol.escapedName) && unescapeLeadingUnderscores(symbol.escapedName),
        recursionId: recursionToken,
        isTuple: objectFlags & 8 /* Tuple */ ? true : void 0,
        unionTypes: type.flags & 1048576 /* Union */ ? (_p = type.types) == null ? void 0 : _p.map((t) => t.id) : void 0,
        intersectionTypes: type.flags & 2097152 /* Intersection */ ? type.types.map((t) => t.id) : void 0,
        aliasTypeArguments: (_q = type.aliasTypeArguments) == null ? void 0 : _q.map((t) => t.id),
        keyofType: type.flags & 4194304 /* Index */ ? (_r = type.type) == null ? void 0 : _r.id : void 0,
        ...indexedAccessProperties,
        ...referenceProperties,
        ...conditionalProperties,
        ...substitutionProperties,
        ...reverseMappedProperties,
        ...evolvingArrayProperties,
        destructuringPattern: getLocation(type.pattern),
        firstDeclaration: getLocation((_s = symbol == null ? void 0 : symbol.declarations) == null ? void 0 : _s[0]),
        flags: Debug.formatTypeFlags(type.flags).split("|"),
        display
      };
      fs.writeSync(typesFd, JSON.stringify(descriptor));
      if (i < numTypes - 1) {
        fs.writeSync(typesFd, ",\n");
      }
    }
    fs.writeSync(typesFd, "]\n");
    fs.closeSync(typesFd);
    mark("endDumpTypes");
    measure("Dump types", "beginDumpTypes", "endDumpTypes");
  }
  function dumpLegend() {
    if (!legendPath) {
      return;
    }
    fs.writeFileSync(legendPath, JSON.stringify(legend));
  }
  tracingEnabled2.dumpLegend = dumpLegend;
})(tracingEnabled || (tracingEnabled = {}));
var startTracing = tracingEnabled.startTracing;
var dumpTracingLegend = tracingEnabled.dumpLegend;

// src/compiler/types.ts
var SyntaxKind = /* @__PURE__ */ ((SyntaxKind4) => {
  SyntaxKind4[SyntaxKind4["Unknown"] = 0] = "Unknown";
  SyntaxKind4[SyntaxKind4["EndOfFileToken"] = 1] = "EndOfFileToken";
  SyntaxKind4[SyntaxKind4["SingleLineCommentTrivia"] = 2] = "SingleLineCommentTrivia";
  SyntaxKind4[SyntaxKind4["MultiLineCommentTrivia"] = 3] = "MultiLineCommentTrivia";
  SyntaxKind4[SyntaxKind4["NewLineTrivia"] = 4] = "NewLineTrivia";
  SyntaxKind4[SyntaxKind4["WhitespaceTrivia"] = 5] = "WhitespaceTrivia";
  SyntaxKind4[SyntaxKind4["ShebangTrivia"] = 6] = "ShebangTrivia";
  SyntaxKind4[SyntaxKind4["ConflictMarkerTrivia"] = 7] = "ConflictMarkerTrivia";
  SyntaxKind4[SyntaxKind4["NonTextFileMarkerTrivia"] = 8] = "NonTextFileMarkerTrivia";
  SyntaxKind4[SyntaxKind4["NumericLiteral"] = 9] = "NumericLiteral";
  SyntaxKind4[SyntaxKind4["BigIntLiteral"] = 10] = "BigIntLiteral";
  SyntaxKind4[SyntaxKind4["StringLiteral"] = 11] = "StringLiteral";
  SyntaxKind4[SyntaxKind4["JsxText"] = 12] = "JsxText";
  SyntaxKind4[SyntaxKind4["JsxTextAllWhiteSpaces"] = 13] = "JsxTextAllWhiteSpaces";
  SyntaxKind4[SyntaxKind4["RegularExpressionLiteral"] = 14] = "RegularExpressionLiteral";
  SyntaxKind4[SyntaxKind4["NoSubstitutionTemplateLiteral"] = 15] = "NoSubstitutionTemplateLiteral";
  SyntaxKind4[SyntaxKind4["TemplateHead"] = 16] = "TemplateHead";
  SyntaxKind4[SyntaxKind4["TemplateMiddle"] = 17] = "TemplateMiddle";
  SyntaxKind4[SyntaxKind4["TemplateTail"] = 18] = "TemplateTail";
  SyntaxKind4[SyntaxKind4["OpenBraceToken"] = 19] = "OpenBraceToken";
  SyntaxKind4[SyntaxKind4["CloseBraceToken"] = 20] = "CloseBraceToken";
  SyntaxKind4[SyntaxKind4["OpenParenToken"] = 21] = "OpenParenToken";
  SyntaxKind4[SyntaxKind4["CloseParenToken"] = 22] = "CloseParenToken";
  SyntaxKind4[SyntaxKind4["OpenBracketToken"] = 23] = "OpenBracketToken";
  SyntaxKind4[SyntaxKind4["CloseBracketToken"] = 24] = "CloseBracketToken";
  SyntaxKind4[SyntaxKind4["DotToken"] = 25] = "DotToken";
  SyntaxKind4[SyntaxKind4["DotDotDotToken"] = 26] = "DotDotDotToken";
  SyntaxKind4[SyntaxKind4["SemicolonToken"] = 27] = "SemicolonToken";
  SyntaxKind4[SyntaxKind4["CommaToken"] = 28] = "CommaToken";
  SyntaxKind4[SyntaxKind4["QuestionDotToken"] = 29] = "QuestionDotToken";
  SyntaxKind4[SyntaxKind4["LessThanToken"] = 30] = "LessThanToken";
  SyntaxKind4[SyntaxKind4["LessThanSlashToken"] = 31] = "LessThanSlashToken";
  SyntaxKind4[SyntaxKind4["GreaterThanToken"] = 32] = "GreaterThanToken";
  SyntaxKind4[SyntaxKind4["LessThanEqualsToken"] = 33] = "LessThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanEqualsToken"] = 34] = "GreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsEqualsToken"] = 35] = "EqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["ExclamationEqualsToken"] = 36] = "ExclamationEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsEqualsEqualsToken"] = 37] = "EqualsEqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["ExclamationEqualsEqualsToken"] = 38] = "ExclamationEqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsGreaterThanToken"] = 39] = "EqualsGreaterThanToken";
  SyntaxKind4[SyntaxKind4["PlusToken"] = 40] = "PlusToken";
  SyntaxKind4[SyntaxKind4["MinusToken"] = 41] = "MinusToken";
  SyntaxKind4[SyntaxKind4["AsteriskToken"] = 42] = "AsteriskToken";
  SyntaxKind4[SyntaxKind4["AsteriskAsteriskToken"] = 43] = "AsteriskAsteriskToken";
  SyntaxKind4[SyntaxKind4["SlashToken"] = 44] = "SlashToken";
  SyntaxKind4[SyntaxKind4["PercentToken"] = 45] = "PercentToken";
  SyntaxKind4[SyntaxKind4["PlusPlusToken"] = 46] = "PlusPlusToken";
  SyntaxKind4[SyntaxKind4["MinusMinusToken"] = 47] = "MinusMinusToken";
  SyntaxKind4[SyntaxKind4["LessThanLessThanToken"] = 48] = "LessThanLessThanToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanToken"] = 49] = "GreaterThanGreaterThanToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanGreaterThanToken"] = 50] = "GreaterThanGreaterThanGreaterThanToken";
  SyntaxKind4[SyntaxKind4["AmpersandToken"] = 51] = "AmpersandToken";
  SyntaxKind4[SyntaxKind4["BarToken"] = 52] = "BarToken";
  SyntaxKind4[SyntaxKind4["CaretToken"] = 53] = "CaretToken";
  SyntaxKind4[SyntaxKind4["ExclamationToken"] = 54] = "ExclamationToken";
  SyntaxKind4[SyntaxKind4["TildeToken"] = 55] = "TildeToken";
  SyntaxKind4[SyntaxKind4["AmpersandAmpersandToken"] = 56] = "AmpersandAmpersandToken";
  SyntaxKind4[SyntaxKind4["BarBarToken"] = 57] = "BarBarToken";
  SyntaxKind4[SyntaxKind4["QuestionToken"] = 58] = "QuestionToken";
  SyntaxKind4[SyntaxKind4["ColonToken"] = 59] = "ColonToken";
  SyntaxKind4[SyntaxKind4["AtToken"] = 60] = "AtToken";
  SyntaxKind4[SyntaxKind4["QuestionQuestionToken"] = 61] = "QuestionQuestionToken";
  SyntaxKind4[SyntaxKind4["BacktickToken"] = 62] = "BacktickToken";
  SyntaxKind4[SyntaxKind4["HashToken"] = 63] = "HashToken";
  SyntaxKind4[SyntaxKind4["EqualsToken"] = 64] = "EqualsToken";
  SyntaxKind4[SyntaxKind4["PlusEqualsToken"] = 65] = "PlusEqualsToken";
  SyntaxKind4[SyntaxKind4["MinusEqualsToken"] = 66] = "MinusEqualsToken";
  SyntaxKind4[SyntaxKind4["AsteriskEqualsToken"] = 67] = "AsteriskEqualsToken";
  SyntaxKind4[SyntaxKind4["AsteriskAsteriskEqualsToken"] = 68] = "AsteriskAsteriskEqualsToken";
  SyntaxKind4[SyntaxKind4["SlashEqualsToken"] = 69] = "SlashEqualsToken";
  SyntaxKind4[SyntaxKind4["PercentEqualsToken"] = 70] = "PercentEqualsToken";
  SyntaxKind4[SyntaxKind4["LessThanLessThanEqualsToken"] = 71] = "LessThanLessThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanEqualsToken"] = 72] = "GreaterThanGreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanGreaterThanEqualsToken"] = 73] = "GreaterThanGreaterThanGreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["AmpersandEqualsToken"] = 74] = "AmpersandEqualsToken";
  SyntaxKind4[SyntaxKind4["BarEqualsToken"] = 75] = "BarEqualsToken";
  SyntaxKind4[SyntaxKind4["BarBarEqualsToken"] = 76] = "BarBarEqualsToken";
  SyntaxKind4[SyntaxKind4["AmpersandAmpersandEqualsToken"] = 77] = "AmpersandAmpersandEqualsToken";
  SyntaxKind4[SyntaxKind4["QuestionQuestionEqualsToken"] = 78] = "QuestionQuestionEqualsToken";
  SyntaxKind4[SyntaxKind4["CaretEqualsToken"] = 79] = "CaretEqualsToken";
  SyntaxKind4[SyntaxKind4["Identifier"] = 80] = "Identifier";
  SyntaxKind4[SyntaxKind4["PrivateIdentifier"] = 81] = "PrivateIdentifier";
  SyntaxKind4[SyntaxKind4["JSDocCommentTextToken"] = 82] = "JSDocCommentTextToken";
  SyntaxKind4[SyntaxKind4["BreakKeyword"] = 83] = "BreakKeyword";
  SyntaxKind4[SyntaxKind4["CaseKeyword"] = 84] = "CaseKeyword";
  SyntaxKind4[SyntaxKind4["CatchKeyword"] = 85] = "CatchKeyword";
  SyntaxKind4[SyntaxKind4["ClassKeyword"] = 86] = "ClassKeyword";
  SyntaxKind4[SyntaxKind4["ConstKeyword"] = 87] = "ConstKeyword";
  SyntaxKind4[SyntaxKind4["ContinueKeyword"] = 88] = "ContinueKeyword";
  SyntaxKind4[SyntaxKind4["DebuggerKeyword"] = 89] = "DebuggerKeyword";
  SyntaxKind4[SyntaxKind4["DefaultKeyword"] = 90] = "DefaultKeyword";
  SyntaxKind4[SyntaxKind4["DeleteKeyword"] = 91] = "DeleteKeyword";
  SyntaxKind4[SyntaxKind4["DoKeyword"] = 92] = "DoKeyword";
  SyntaxKind4[SyntaxKind4["ElseKeyword"] = 93] = "ElseKeyword";
  SyntaxKind4[SyntaxKind4["EnumKeyword"] = 94] = "EnumKeyword";
  SyntaxKind4[SyntaxKind4["ExportKeyword"] = 95] = "ExportKeyword";
  SyntaxKind4[SyntaxKind4["ExtendsKeyword"] = 96] = "ExtendsKeyword";
  SyntaxKind4[SyntaxKind4["FalseKeyword"] = 97] = "FalseKeyword";
  SyntaxKind4[SyntaxKind4["FinallyKeyword"] = 98] = "FinallyKeyword";
  SyntaxKind4[SyntaxKind4["ForKeyword"] = 99] = "ForKeyword";
  SyntaxKind4[SyntaxKind4["FunctionKeyword"] = 100] = "FunctionKeyword";
  SyntaxKind4[SyntaxKind4["IfKeyword"] = 101] = "IfKeyword";
  SyntaxKind4[SyntaxKind4["ImportKeyword"] = 102] = "ImportKeyword";
  SyntaxKind4[SyntaxKind4["InKeyword"] = 103] = "InKeyword";
  SyntaxKind4[SyntaxKind4["InstanceOfKeyword"] = 104] = "InstanceOfKeyword";
  SyntaxKind4[SyntaxKind4["NewKeyword"] = 105] = "NewKeyword";
  SyntaxKind4[SyntaxKind4["NullKeyword"] = 106] = "NullKeyword";
  SyntaxKind4[SyntaxKind4["ReturnKeyword"] = 107] = "ReturnKeyword";
  SyntaxKind4[SyntaxKind4["SuperKeyword"] = 108] = "SuperKeyword";
  SyntaxKind4[SyntaxKind4["SwitchKeyword"] = 109] = "SwitchKeyword";
  SyntaxKind4[SyntaxKind4["ThisKeyword"] = 110] = "ThisKeyword";
  SyntaxKind4[SyntaxKind4["ThrowKeyword"] = 111] = "ThrowKeyword";
  SyntaxKind4[SyntaxKind4["TrueKeyword"] = 112] = "TrueKeyword";
  SyntaxKind4[SyntaxKind4["TryKeyword"] = 113] = "TryKeyword";
  SyntaxKind4[SyntaxKind4["TypeOfKeyword"] = 114] = "TypeOfKeyword";
  SyntaxKind4[SyntaxKind4["VarKeyword"] = 115] = "VarKeyword";
  SyntaxKind4[SyntaxKind4["VoidKeyword"] = 116] = "VoidKeyword";
  SyntaxKind4[SyntaxKind4["WhileKeyword"] = 117] = "WhileKeyword";
  SyntaxKind4[SyntaxKind4["WithKeyword"] = 118] = "WithKeyword";
  SyntaxKind4[SyntaxKind4["ImplementsKeyword"] = 119] = "ImplementsKeyword";
  SyntaxKind4[SyntaxKind4["InterfaceKeyword"] = 120] = "InterfaceKeyword";
  SyntaxKind4[SyntaxKind4["LetKeyword"] = 121] = "LetKeyword";
  SyntaxKind4[SyntaxKind4["PackageKeyword"] = 122] = "PackageKeyword";
  SyntaxKind4[SyntaxKind4["PrivateKeyword"] = 123] = "PrivateKeyword";
  SyntaxKind4[SyntaxKind4["ProtectedKeyword"] = 124] = "ProtectedKeyword";
  SyntaxKind4[SyntaxKind4["PublicKeyword"] = 125] = "PublicKeyword";
  SyntaxKind4[SyntaxKind4["StaticKeyword"] = 126] = "StaticKeyword";
  SyntaxKind4[SyntaxKind4["YieldKeyword"] = 127] = "YieldKeyword";
  SyntaxKind4[SyntaxKind4["AbstractKeyword"] = 128] = "AbstractKeyword";
  SyntaxKind4[SyntaxKind4["AccessorKeyword"] = 129] = "AccessorKeyword";
  SyntaxKind4[SyntaxKind4["AsKeyword"] = 130] = "AsKeyword";
  SyntaxKind4[SyntaxKind4["AssertsKeyword"] = 131] = "AssertsKeyword";
  SyntaxKind4[SyntaxKind4["AssertKeyword"] = 132] = "AssertKeyword";
  SyntaxKind4[SyntaxKind4["AnyKeyword"] = 133] = "AnyKeyword";
  SyntaxKind4[SyntaxKind4["AsyncKeyword"] = 134] = "AsyncKeyword";
  SyntaxKind4[SyntaxKind4["AwaitKeyword"] = 135] = "AwaitKeyword";
  SyntaxKind4[SyntaxKind4["BooleanKeyword"] = 136] = "BooleanKeyword";
  SyntaxKind4[SyntaxKind4["ConstructorKeyword"] = 137] = "ConstructorKeyword";
  SyntaxKind4[SyntaxKind4["DeclareKeyword"] = 138] = "DeclareKeyword";
  SyntaxKind4[SyntaxKind4["GetKeyword"] = 139] = "GetKeyword";
  SyntaxKind4[SyntaxKind4["InferKeyword"] = 140] = "InferKeyword";
  SyntaxKind4[SyntaxKind4["IntrinsicKeyword"] = 141] = "IntrinsicKeyword";
  SyntaxKind4[SyntaxKind4["IsKeyword"] = 142] = "IsKeyword";
  SyntaxKind4[SyntaxKind4["KeyOfKeyword"] = 143] = "KeyOfKeyword";
  SyntaxKind4[SyntaxKind4["ModuleKeyword"] = 144] = "ModuleKeyword";
  SyntaxKind4[SyntaxKind4["NamespaceKeyword"] = 145] = "NamespaceKeyword";
  SyntaxKind4[SyntaxKind4["NeverKeyword"] = 146] = "NeverKeyword";
  SyntaxKind4[SyntaxKind4["OutKeyword"] = 147] = "OutKeyword";
  SyntaxKind4[SyntaxKind4["ReadonlyKeyword"] = 148] = "ReadonlyKeyword";
  SyntaxKind4[SyntaxKind4["RequireKeyword"] = 149] = "RequireKeyword";
  SyntaxKind4[SyntaxKind4["NumberKeyword"] = 150] = "NumberKeyword";
  SyntaxKind4[SyntaxKind4["ObjectKeyword"] = 151] = "ObjectKeyword";
  SyntaxKind4[SyntaxKind4["SatisfiesKeyword"] = 152] = "SatisfiesKeyword";
  SyntaxKind4[SyntaxKind4["SetKeyword"] = 153] = "SetKeyword";
  SyntaxKind4[SyntaxKind4["StringKeyword"] = 154] = "StringKeyword";
  SyntaxKind4[SyntaxKind4["SymbolKeyword"] = 155] = "SymbolKeyword";
  SyntaxKind4[SyntaxKind4["TypeKeyword"] = 156] = "TypeKeyword";
  SyntaxKind4[SyntaxKind4["UndefinedKeyword"] = 157] = "UndefinedKeyword";
  SyntaxKind4[SyntaxKind4["UniqueKeyword"] = 158] = "UniqueKeyword";
  SyntaxKind4[SyntaxKind4["UnknownKeyword"] = 159] = "UnknownKeyword";
  SyntaxKind4[SyntaxKind4["UsingKeyword"] = 160] = "UsingKeyword";
  SyntaxKind4[SyntaxKind4["FromKeyword"] = 161] = "FromKeyword";
  SyntaxKind4[SyntaxKind4["GlobalKeyword"] = 162] = "GlobalKeyword";
  SyntaxKind4[SyntaxKind4["BigIntKeyword"] = 163] = "BigIntKeyword";
  SyntaxKind4[SyntaxKind4["OverrideKeyword"] = 164] = "OverrideKeyword";
  SyntaxKind4[SyntaxKind4["OfKeyword"] = 165] = "OfKeyword";
  SyntaxKind4[SyntaxKind4["DeferKeyword"] = 166] = "DeferKeyword";
  SyntaxKind4[SyntaxKind4["QualifiedName"] = 167] = "QualifiedName";
  SyntaxKind4[SyntaxKind4["ComputedPropertyName"] = 168] = "ComputedPropertyName";
  SyntaxKind4[SyntaxKind4["TypeParameter"] = 169] = "TypeParameter";
  SyntaxKind4[SyntaxKind4["Parameter"] = 170] = "Parameter";
  SyntaxKind4[SyntaxKind4["Decorator"] = 171] = "Decorator";
  SyntaxKind4[SyntaxKind4["PropertySignature"] = 172] = "PropertySignature";
  SyntaxKind4[SyntaxKind4["PropertyDeclaration"] = 173] = "PropertyDeclaration";
  SyntaxKind4[SyntaxKind4["MethodSignature"] = 174] = "MethodSignature";
  SyntaxKind4[SyntaxKind4["MethodDeclaration"] = 175] = "MethodDeclaration";
  SyntaxKind4[SyntaxKind4["ClassStaticBlockDeclaration"] = 176] = "ClassStaticBlockDeclaration";
  SyntaxKind4[SyntaxKind4["Constructor"] = 177] = "Constructor";
  SyntaxKind4[SyntaxKind4["GetAccessor"] = 178] = "GetAccessor";
  SyntaxKind4[SyntaxKind4["SetAccessor"] = 179] = "SetAccessor";
  SyntaxKind4[SyntaxKind4["CallSignature"] = 180] = "CallSignature";
  SyntaxKind4[SyntaxKind4["ConstructSignature"] = 181] = "ConstructSignature";
  SyntaxKind4[SyntaxKind4["IndexSignature"] = 182] = "IndexSignature";
  SyntaxKind4[SyntaxKind4["TypePredicate"] = 183] = "TypePredicate";
  SyntaxKind4[SyntaxKind4["TypeReference"] = 184] = "TypeReference";
  SyntaxKind4[SyntaxKind4["FunctionType"] = 185] = "FunctionType";
  SyntaxKind4[SyntaxKind4["ConstructorType"] = 186] = "ConstructorType";
  SyntaxKind4[SyntaxKind4["TypeQuery"] = 187] = "TypeQuery";
  SyntaxKind4[SyntaxKind4["TypeLiteral"] = 188] = "TypeLiteral";
  SyntaxKind4[SyntaxKind4["ArrayType"] = 189] = "ArrayType";
  SyntaxKind4[SyntaxKind4["TupleType"] = 190] = "TupleType";
  SyntaxKind4[SyntaxKind4["OptionalType"] = 191] = "OptionalType";
  SyntaxKind4[SyntaxKind4["RestType"] = 192] = "RestType";
  SyntaxKind4[SyntaxKind4["UnionType"] = 193] = "UnionType";
  SyntaxKind4[SyntaxKind4["IntersectionType"] = 194] = "IntersectionType";
  SyntaxKind4[SyntaxKind4["ConditionalType"] = 195] = "ConditionalType";
  SyntaxKind4[SyntaxKind4["InferType"] = 196] = "InferType";
  SyntaxKind4[SyntaxKind4["ParenthesizedType"] = 197] = "ParenthesizedType";
  SyntaxKind4[SyntaxKind4["ThisType"] = 198] = "ThisType";
  SyntaxKind4[SyntaxKind4["TypeOperator"] = 199] = "TypeOperator";
  SyntaxKind4[SyntaxKind4["IndexedAccessType"] = 200] = "IndexedAccessType";
  SyntaxKind4[SyntaxKind4["MappedType"] = 201] = "MappedType";
  SyntaxKind4[SyntaxKind4["LiteralType"] = 202] = "LiteralType";
  SyntaxKind4[SyntaxKind4["NamedTupleMember"] = 203] = "NamedTupleMember";
  SyntaxKind4[SyntaxKind4["TemplateLiteralType"] = 204] = "TemplateLiteralType";
  SyntaxKind4[SyntaxKind4["TemplateLiteralTypeSpan"] = 205] = "TemplateLiteralTypeSpan";
  SyntaxKind4[SyntaxKind4["ImportType"] = 206] = "ImportType";
  SyntaxKind4[SyntaxKind4["ObjectBindingPattern"] = 207] = "ObjectBindingPattern";
  SyntaxKind4[SyntaxKind4["ArrayBindingPattern"] = 208] = "ArrayBindingPattern";
  SyntaxKind4[SyntaxKind4["BindingElement"] = 209] = "BindingElement";
  SyntaxKind4[SyntaxKind4["ArrayLiteralExpression"] = 210] = "ArrayLiteralExpression";
  SyntaxKind4[SyntaxKind4["ObjectLiteralExpression"] = 211] = "ObjectLiteralExpression";
  SyntaxKind4[SyntaxKind4["PropertyAccessExpression"] = 212] = "PropertyAccessExpression";
  SyntaxKind4[SyntaxKind4["ElementAccessExpression"] = 213] = "ElementAccessExpression";
  SyntaxKind4[SyntaxKind4["CallExpression"] = 214] = "CallExpression";
  SyntaxKind4[SyntaxKind4["NewExpression"] = 215] = "NewExpression";
  SyntaxKind4[SyntaxKind4["TaggedTemplateExpression"] = 216] = "TaggedTemplateExpression";
  SyntaxKind4[SyntaxKind4["TypeAssertionExpression"] = 217] = "TypeAssertionExpression";
  SyntaxKind4[SyntaxKind4["ParenthesizedExpression"] = 218] = "ParenthesizedExpression";
  SyntaxKind4[SyntaxKind4["FunctionExpression"] = 219] = "FunctionExpression";
  SyntaxKind4[SyntaxKind4["ArrowFunction"] = 220] = "ArrowFunction";
  SyntaxKind4[SyntaxKind4["DeleteExpression"] = 221] = "DeleteExpression";
  SyntaxKind4[SyntaxKind4["TypeOfExpression"] = 222] = "TypeOfExpression";
  SyntaxKind4[SyntaxKind4["VoidExpression"] = 223] = "VoidExpression";
  SyntaxKind4[SyntaxKind4["AwaitExpression"] = 224] = "AwaitExpression";
  SyntaxKind4[SyntaxKind4["PrefixUnaryExpression"] = 225] = "PrefixUnaryExpression";
  SyntaxKind4[SyntaxKind4["PostfixUnaryExpression"] = 226] = "PostfixUnaryExpression";
  SyntaxKind4[SyntaxKind4["BinaryExpression"] = 227] = "BinaryExpression";
  SyntaxKind4[SyntaxKind4["ConditionalExpression"] = 228] = "ConditionalExpression";
  SyntaxKind4[SyntaxKind4["TemplateExpression"] = 229] = "TemplateExpression";
  SyntaxKind4[SyntaxKind4["YieldExpression"] = 230] = "YieldExpression";
  SyntaxKind4[SyntaxKind4["SpreadElement"] = 231] = "SpreadElement";
  SyntaxKind4[SyntaxKind4["ClassExpression"] = 232] = "ClassExpression";
  SyntaxKind4[SyntaxKind4["OmittedExpression"] = 233] = "OmittedExpression";
  SyntaxKind4[SyntaxKind4["ExpressionWithTypeArguments"] = 234] = "ExpressionWithTypeArguments";
  SyntaxKind4[SyntaxKind4["AsExpression"] = 235] = "AsExpression";
  SyntaxKind4[SyntaxKind4["NonNullExpression"] = 236] = "NonNullExpression";
  SyntaxKind4[SyntaxKind4["MetaProperty"] = 237] = "MetaProperty";
  SyntaxKind4[SyntaxKind4["SyntheticExpression"] = 238] = "SyntheticExpression";
  SyntaxKind4[SyntaxKind4["SatisfiesExpression"] = 239] = "SatisfiesExpression";
  SyntaxKind4[SyntaxKind4["TemplateSpan"] = 240] = "TemplateSpan";
  SyntaxKind4[SyntaxKind4["SemicolonClassElement"] = 241] = "SemicolonClassElement";
  SyntaxKind4[SyntaxKind4["Block"] = 242] = "Block";
  SyntaxKind4[SyntaxKind4["EmptyStatement"] = 243] = "EmptyStatement";
  SyntaxKind4[SyntaxKind4["VariableStatement"] = 244] = "VariableStatement";
  SyntaxKind4[SyntaxKind4["ExpressionStatement"] = 245] = "ExpressionStatement";
  SyntaxKind4[SyntaxKind4["IfStatement"] = 246] = "IfStatement";
  SyntaxKind4[SyntaxKind4["DoStatement"] = 247] = "DoStatement";
  SyntaxKind4[SyntaxKind4["WhileStatement"] = 248] = "WhileStatement";
  SyntaxKind4[SyntaxKind4["ForStatement"] = 249] = "ForStatement";
  SyntaxKind4[SyntaxKind4["ForInStatement"] = 250] = "ForInStatement";
  SyntaxKind4[SyntaxKind4["ForOfStatement"] = 251] = "ForOfStatement";
  SyntaxKind4[SyntaxKind4["ContinueStatement"] = 252] = "ContinueStatement";
  SyntaxKind4[SyntaxKind4["BreakStatement"] = 253] = "BreakStatement";
  SyntaxKind4[SyntaxKind4["ReturnStatement"] = 254] = "ReturnStatement";
  SyntaxKind4[SyntaxKind4["WithStatement"] = 255] = "WithStatement";
  SyntaxKind4[SyntaxKind4["SwitchStatement"] = 256] = "SwitchStatement";
  SyntaxKind4[SyntaxKind4["LabeledStatement"] = 257] = "LabeledStatement";
  SyntaxKind4[SyntaxKind4["ThrowStatement"] = 258] = "ThrowStatement";
  SyntaxKind4[SyntaxKind4["TryStatement"] = 259] = "TryStatement";
  SyntaxKind4[SyntaxKind4["DebuggerStatement"] = 260] = "DebuggerStatement";
  SyntaxKind4[SyntaxKind4["VariableDeclaration"] = 261] = "VariableDeclaration";
  SyntaxKind4[SyntaxKind4["VariableDeclarationList"] = 262] = "VariableDeclarationList";
  SyntaxKind4[SyntaxKind4["FunctionDeclaration"] = 263] = "FunctionDeclaration";
  SyntaxKind4[SyntaxKind4["ClassDeclaration"] = 264] = "ClassDeclaration";
  SyntaxKind4[SyntaxKind4["InterfaceDeclaration"] = 265] = "InterfaceDeclaration";
  SyntaxKind4[SyntaxKind4["TypeAliasDeclaration"] = 266] = "TypeAliasDeclaration";
  SyntaxKind4[SyntaxKind4["EnumDeclaration"] = 267] = "EnumDeclaration";
  SyntaxKind4[SyntaxKind4["ModuleDeclaration"] = 268] = "ModuleDeclaration";
  SyntaxKind4[SyntaxKind4["ModuleBlock"] = 269] = "ModuleBlock";
  SyntaxKind4[SyntaxKind4["CaseBlock"] = 270] = "CaseBlock";
  SyntaxKind4[SyntaxKind4["NamespaceExportDeclaration"] = 271] = "NamespaceExportDeclaration";
  SyntaxKind4[SyntaxKind4["ImportEqualsDeclaration"] = 272] = "ImportEqualsDeclaration";
  SyntaxKind4[SyntaxKind4["ImportDeclaration"] = 273] = "ImportDeclaration";
  SyntaxKind4[SyntaxKind4["ImportClause"] = 274] = "ImportClause";
  SyntaxKind4[SyntaxKind4["NamespaceImport"] = 275] = "NamespaceImport";
  SyntaxKind4[SyntaxKind4["NamedImports"] = 276] = "NamedImports";
  SyntaxKind4[SyntaxKind4["ImportSpecifier"] = 277] = "ImportSpecifier";
  SyntaxKind4[SyntaxKind4["ExportAssignment"] = 278] = "ExportAssignment";
  SyntaxKind4[SyntaxKind4["ExportDeclaration"] = 279] = "ExportDeclaration";
  SyntaxKind4[SyntaxKind4["NamedExports"] = 280] = "NamedExports";
  SyntaxKind4[SyntaxKind4["NamespaceExport"] = 281] = "NamespaceExport";
  SyntaxKind4[SyntaxKind4["ExportSpecifier"] = 282] = "ExportSpecifier";
  SyntaxKind4[SyntaxKind4["MissingDeclaration"] = 283] = "MissingDeclaration";
  SyntaxKind4[SyntaxKind4["ExternalModuleReference"] = 284] = "ExternalModuleReference";
  SyntaxKind4[SyntaxKind4["JsxElement"] = 285] = "JsxElement";
  SyntaxKind4[SyntaxKind4["JsxSelfClosingElement"] = 286] = "JsxSelfClosingElement";
  SyntaxKind4[SyntaxKind4["JsxOpeningElement"] = 287] = "JsxOpeningElement";
  SyntaxKind4[SyntaxKind4["JsxClosingElement"] = 288] = "JsxClosingElement";
  SyntaxKind4[SyntaxKind4["JsxFragment"] = 289] = "JsxFragment"