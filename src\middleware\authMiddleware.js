const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

const sessionAuth = async (req, res, next) => {
  console.log('\n=== AUTH MIDDLEWARE TRIGGERED ===');
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl}`);

  try {
    // Debug incoming request details
    console.log('\n[Request Details]');
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Cookies:', req.cookies);
    console.log('Body:', req.body);
    console.log('Query:', req.query);
    console.log('Params:', req.params);

    // 1. Extract token and WAX account
    const authHeader = req.headers.authorization;
    console.log('\n[Auth Header]', authHeader ? `${authHeader.substring(0, 15)}...` : 'MISSING');

    // Safely extract token from cookies or Authorization header
    const token = (req.cookies && req.cookies.sessionToken) ||
                 (authHeader && authHeader.startsWith('Bearer ') ? authHeader.split(' ')[1] : authHeader);
    // Extract WAX account from various possible locations
    let waxAccount = req.body.user || req.query.user || req.params.user || req.params.id;

    console.log('\n[Path Info]');
    console.log('Original URL:', req.originalUrl);
    console.log('Path:', req.path);

    // If waxAccount is still not found, try to extract it from the URL path
    // For routes like /players/3ahb2.wam
    if (!waxAccount && req.originalUrl) {
      const pathParts = req.originalUrl.split('/');
      if (pathParts.length > 2) {
        const potentialWaxId = pathParts[2].split('?')[0]; // Remove any query params
        if (potentialWaxId && potentialWaxId.includes('.')) {
          console.log('\n[Extracted WAX ID from URL]:', potentialWaxId);
          waxAccount = potentialWaxId;
        }
      }
    }

    console.log('\n[Extracted Credentials]');
    console.log('Token:', token ? `${token.substring(0, 10)}...${token.substring(token.length - 5)}` : 'MISSING');
    console.log('Token Length:', token ? token.length : 'N/A');
    console.log('WAX Account:', waxAccount || 'MISSING');

    if (!token || !waxAccount) {
      console.error('\n[Auth Failed] Missing credentials');
      return res.status(401).json({ error: 'Missing authentication credentials' });
    }

    // 2. Verify session
    console.log('\n[Database Query] Checking session for:', waxAccount);

    try {
      const result = await pool.query(
        `SELECT token, expires_at FROM user_sessions
         WHERE user_id = $1`,
        [waxAccount]
      );

      console.log('\n[Query Result]', 'Rows found:', result.rows.length);

      if (result.rows.length === 0) {
        console.error('\n[Auth Failed] No session found for user');
        return res.status(401).json({ error: 'No session found for user' });
      }

      const session = result.rows[0];

      // Check if session is expired
      const now = new Date();
      if (session.expires_at < now) {
        console.error('\n[Auth Failed] Session expired at:', session.expires_at);
        return res.status(401).json({ error: 'Session expired' });
      }

      console.log('\n[Session Found]');
      console.log('Expires At:', session.expires_at);
      console.log('Stored Token Hash Length:', session.token.length);
      console.log('Stored Token Hash Prefix:', session.token.substring(0, 10));

      // Debug token comparison
      const isMatch = await bcrypt.compare(token, session.token);
      console.log('Token Match:', isMatch ? 'VALID' : 'INVALID');

      if (!isMatch) {
        console.error('\n[Auth Failed] Token mismatch');
        console.error('Provided token does not match stored hash');
        return res.status(401).json({ error: 'Invalid session token' });
      }
    } catch (dbError) {
      console.error('\n[Database Error]', dbError.message);
      console.error('Stack:', dbError.stack);
      return res.status(500).json({ error: 'Database error during authentication' });
    }

    // 3. Attach user data
    req.authenticatedUser = { waxAccount };
    console.log('\n[Auth Success] Proceeding to route handler');
    next();
  } catch (error) {
    console.error('\n!!! AUTH MIDDLEWARE ERROR !!!');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

const requestLogger = (req, res, next) => {
  console.log(`\n[${new Date().toISOString()}] ${req.method} ${req.path}`);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  next();
};

module.exports = { sessionAuth, requestLogger };
