const axios = require("axios");
const { app_url } = require("../../config");

async function getZones() {
  try {
    const response = await axios.get(`${app_url}/players/zones`);
    const data = response.data;
    console.log(data);
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

async function getTile(data, grid_4, grid_16, grid_256) {
  let current_map;
console.log(data)
  for (m in data) {
    var z_id = data[m].id;
    var mapgrid_4 = data[m].mapgrid_4;
    var mapgrid_16 = data[m].mapgrid_16;
    var name = data[m].zone_name;
    var type = data[m].zone_type;
    var bal = data[m].gxp_paid;
    var req = data[m].gxp_required;
    var status = data[m].status;
    if (mapgrid_4 == grid_4 && mapgrid_16 == grid_16) {
  current_map = data[m].data.locales;
      console.log(current_map)
    }
  }
  if (current_map) {
    for ( n in current_map) {
      if (current_map[n].Locale == grid_256) {
        const terrain = current_map[n].Tile;
console.log(terrain)
        return terrain;
      }
    }
  }

  throw new Error('Tile not found');
}

module.exports = {
getZones,
getTile
};
