{"name": "ts-node", "version": "9.1.1", "description": "TypeScript execution environment and REPL for node.js, with source map support", "main": "dist/index.js", "exports": {".": "./dist/index.js", "./package": "./package.json", "./package.json": "./package.json", "./dist/bin": "./dist/bin.js", "./dist/bin.js": "./dist/bin.js", "./dist/bin-transpile": "./dist/bin-transpile.js", "./dist/bin-transpile.js": "./dist/bin-transpile.js", "./dist/bin-script": "./dist/bin-script.js", "./dist/bin-script.js": "./dist/bin-script.js", "./register": "./register/index.js", "./register/files": "./register/files.js", "./register/transpile-only": "./register/transpile-only.js", "./register/type-check": "./register/type-check.js", "./esm": "./esm.mjs", "./esm.mjs": "./esm.mjs", "./esm/transpile-only": "./esm/transpile-only.mjs", "./esm/transpile-only.mjs": "./esm/transpile-only.mjs"}, "types": "dist/index.d.ts", "bin": {"ts-node": "dist/bin.js", "ts-script": "dist/bin-script-deprecated.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js"}, "files": ["dist/", "dist-raw/", "register/", "esm/", "esm.mjs", "LICENSE", "tsconfig.schema.json", "tsconfig.schemastore-schema.json"], "scripts": {"lint": "tslint \"src/**/*.ts\" --project tsconfig.json", "lint-fix": "tslint \"src/**/*.ts\" --project tsconfig.json --fix", "clean": "rimraf dist && rimraf tsconfig.schema.json && rimraf tsconfig.schemastore-schema.json && rimraf tests/ts-node-packed.tgz", "build": "npm run build-nopack && npm run build-pack", "build-nopack": "npm run clean && npm run build-tsc && npm run build-configSchema", "build-tsc": "tsc", "build-configSchema": "typescript-json-schema --topRef --refs --validationKeywords allOf --out tsconfig.schema.json tsconfig.json TsConfigSchema && node --require ./register ./scripts/create-merged-schema", "build-pack": "node ./scripts/build-pack.js", "test-spec": "mocha dist/**/*.spec.js -R spec --bail", "test-cov": "nyc mocha -- \"dist/**/*.spec.js\" -R spec --bail", "test": "npm run build && npm run lint && npm run test-cov --", "coverage-report": "nyc report --reporter=lcov", "prepare": "npm run build-nopack"}, "engines": {"node": ">=10.0.0"}, "repository": {"type": "git", "url": "git://github.com/TypeStrong/ts-node.git"}, "keywords": ["typescript", "node", "runtime", "environment", "ts", "compiler"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/ts-node/issues"}, "homepage": "https://github.com/TypeStrong/ts-node", "devDependencies": {"@types/chai": "^4.0.4", "@types/diff": "^4.0.2", "@types/lodash": "^4.14.151", "@types/mocha": "^5.2.7", "@types/node": "13.13.5", "@types/proxyquire": "^1.3.28", "@types/react": "^16.0.2", "@types/rimraf": "^3.0.0", "@types/semver": "^7.1.0", "@types/source-map-support": "^0.5.0", "axios": "^0.19.0", "chai": "^4.0.1", "get-stream": "^6.0.0", "lodash": "^4.17.15", "mocha": "^6.2.2", "ntypescript": "^1.201507091536.1", "nyc": "^15.0.1", "proxyquire": "^2.0.0", "react": "^16.0.0", "rimraf": "^3.0.0", "semver": "^7.1.3", "tslint": "^6.1.0", "tslint-config-standard": "^9.0.0", "typescript": "4.1.2", "typescript-json-schema": "^0.42.0", "util.promisify": "^1.0.1"}, "peerDependencies": {"typescript": ">=2.7"}, "dependencies": {"arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "source-map-support": "^0.5.17", "yn": "3.1.1"}}