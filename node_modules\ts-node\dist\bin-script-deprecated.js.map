{"version": 3, "file": "bin-script-deprecated.js", "sourceRoot": "", "sources": ["../src/bin-script-deprecated.ts"], "names": [], "mappings": ";;;AAEA,+BAA4B;AAE5B,OAAO,CAAC,IAAI,CACV,8EAA8E,EAC9E,mCAAmC,CACpC,CAAA;AAED,UAAI,CAAC,SAAS,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAA", "sourcesContent": ["#!/usr/bin/env node\n\nimport { main } from './bin'\n\nconsole.warn(\n  'ts-script has been deprecated and will be removed in the next major release.',\n  'Please use ts-node-script instead'\n)\n\nmain(undefined, { '--script-mode': true })\n"]}