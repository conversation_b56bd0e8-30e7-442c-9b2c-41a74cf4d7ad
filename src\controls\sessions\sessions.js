const pool = require("../db");
const queries = require("../queries");
const bcrypt = require('bcrypt');

const addSessionToken = async (req, res) => {
  const { token, user } = req.body;
 
  // Ensure we're hashing the raw token, not a Bearer token
  const tokenToHash = token.startsWith('Bearer ') ? token.split(' ')[1] : token;

  const hashedToken = await bcrypt.hash(tokenToHash, 2);
  const hashedTokenStr = hashedToken;

  try {
    try {
      const results = await pool.query(queries.getby.getUserSessionById, [user]);
      if (!results.rows.length) {
        try {
          await pool.query(queries.add.addUserSession, [user, hashedTokenStr]);
          return res.status(201).send('New session created');
        } catch (err) {
          return res.status(500).send('Error adding new user session');
        }
      }
      try {
        const userSession = results.rows[0];
        const now = new Date();
        if (userSession.expires_at < now) {
          try {
            await pool.query(queries.add.addUserSession, [user, hashedTokenStr]);
            return res.status(201).send('Updated with the new token');
          } catch (err) {
            return res.status(500).send('Error updating expired user session');
          }
        }
      } catch (err) {
        return res.status(500).send('Error getting user session');
      }

      try {
        const results = await pool.query(queries.getby.getUserSessionById, [user]);
        const userSession = results.rows[0];
        const storedHash = userSession.token;

        try {
          const isMatch = await bcrypt.compare(tokenToHash, storedHash);

          if (isMatch) {
            return res.status(200).send('Token valid');
          } else {
            return res.status(401).send('Token invalid');
          }
        } catch (bcryptError) {
          return res.status(500).send('Error comparing tokens');
        }
      } catch (err) {
        return res.status(500).send('Error validating token');
      }

    } catch (err) {
      return res.status(500).send('Error querying user session');
    }
  } catch (err) {
    return res.status(500).send('Error hashing token');
  }
};

const getUserSessionById = async (req, res) => {
  try {
    const authorizationHeader = req.headers.authorization;

    // Check if the header is in Bearer format
    let token = null;
    if (authorizationHeader && authorizationHeader.startsWith('Bearer ')) {
      token = authorizationHeader.split(' ')[1];
    } else {
      // If not in Bearer format, use the whole header as token (for backward compatibility)
      token = authorizationHeader;
    }

    const wax_id = req.params.user;

    pool.query(queries.getby.getUserSessionById, [wax_id], async (error, results) => {
      if (error) {
        return res.status(500).send('Internal Server Error');
      }

      // Check if we have results
      if (!results.rows || results.rows.length === 0) {
        return res.status(401).json({ error: 'No active session found' });
      }

      const storedToken = results.rows[0].token;

      // Check if token is expired
      const expiresAt = results.rows[0].expires_at;
      const now = new Date();

      if (expiresAt < now) {
        return res.status(401).json({ error: 'Session expired' });
      }

      try {
        // Try comparing with the Bearer token first
        let isMatch = false;
        if (token) {
          try {
            isMatch = await bcrypt.compare(token, storedToken);
          } catch (bcryptError) {
          }
        }

        if (isMatch) {
          // Return the session data in the format expected by the client
          return res.status(200).json(results.rows);
        } else {
          return res.status(401).send('Token invalid');
        }
      } catch (bcryptError) {
        return res.status(500).send('Error validating token');
      }
    });
  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
};

const getUserSessionByToken = (req, res) => {
  var token = req.params.id;
  pool.query(queries.getby.getUserSessionByToken, [token], (error, results) => {
    if (error) {
      res.status(500).send('Internal Server Error');
    } else {
      res.status(200).json(results.rows);
    }
  });
};

const removeUserSession = (req, res) => {
  var wax_id = req.params.wax_id;
  pool.query(queries.remove.removeUserSession, [wax_id], (error, results) => {
    res.status(200).send("Session removed successfully for user: " + wax_id);
  });
} 

module.exports = {
  addSessionToken,
  getUserSessionById,
  getUserSessionByToken,
  removeUserSession
};