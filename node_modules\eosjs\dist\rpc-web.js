"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RpcError = exports.JsonRpc = void 0;
var eosjs_jsonrpc_1 = require("./eosjs-jsonrpc");
Object.defineProperty(exports, "JsonRpc", { enumerable: true, get: function () { return eosjs_jsonrpc_1.JsonRpc; } });
var eosjs_rpcerror_1 = require("./eosjs-rpcerror");
Object.defineProperty(exports, "RpcError", { enumerable: true, get: function () { return eosjs_rpcerror_1.RpcError; } });
//# sourceMappingURL=rpc-web.js.map