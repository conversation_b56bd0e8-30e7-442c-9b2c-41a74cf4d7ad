// The general.js and daily.js are files run on a seperate heroku instance for systems operations that occur away from the clients
// access. Treat the folder 'system-server' files as belonging to a seperate app from the main app.
const fetch = require('node-fetch');
const express = require("express");
const axios = require("axios");
const { app_url } = require("../config");

const {
standardPool,
forestPool,
healthyFruitsPool,
waterPool,
grassPool,
castlePool
} = require("../game/data/reward-pools");
const {
getZones,
getTile
} = require("../game/api/map-api");
const {
isZonePaidFully,
unlockMapZone
} = require("../game/db/zones");
const {
getAdventures,
getInProgressAdventures,
updateAdventureProgress,
updateAdventure
} = require("../game/db/adventures");
const { getPlayerBalances } = require("../game/api/player");
const { removeGameLogs } = require("../game/api/logs-api");
const { checkAndResetExpiredLimits } = require("../game/api/global-limit");
const { checkAndResetExpiredPlayerLimits } = require("../game/api/player-limit");
const systemLogging = require("../game/api/system-logging");

var playerBalances = {};
var allNaps = {};
var standardNapRate = 3;
var bonusHouseRate = 1.5;
var playerTotalCreatures;


function resetZonePrices() {
zonePrices = [];
zoneNames = [];
}

async function main() {
  try {
    // Log system startup
    await systemLogging.addSystemLog(
      'INFO',
      'SYSTEM',
      null,
      'System processes started successfully',
      {
        timestamp: new Date().toISOString(),
        processes: ['reward-limits', 'player-limits', 'adventure-progress', 'log-cleanup']
      }
    );

    playerBalances = await getPlayerBalances();
    const zoneData = await getZones();
    const adv_data = await getAdventures();
    const in_progress_adventures = await getInProgressAdventures(adv_data);
    const move_players = await updateAdventureProgress(
      in_progress_adventures,
      adv_data
    );

    // remove logs that are old from player logs - ALL players
    const logsCleanUp = await removeGameLogs();
    const isZoneUnlocked = await isZonePaidFully();

    // Check and reset expired global reward limits
    const globalLimitsReset = await checkAndResetExpiredLimits();

    // Log that limits were reset only if a reset occurred
    if (globalLimitsReset) {
      await systemLogging.addSystemLog(
        'INFO',
        'LIMIT',
        null,
        'Daily reward limits reset successfully',
        {
          reset_types: ['NFT', 'DUST', 'GXP'],
          timestamp: new Date().toISOString()
        }
      );
    }

    // Check and reset expired player reward limits
    const playerLimitsReset = await checkAndResetExpiredPlayerLimits();
    if (playerLimitsReset) {
      await systemLogging.addSystemLog(
        'INFO',
        'LIMIT',
        null,
        'Daily player reward limits reset successfully',
        {
          reset_types: ['NFT', 'DUST', 'GXP'],
          timestamp: new Date().toISOString()
        }
      );
    }

   } catch (error) {
    console.error('Error:', error);

    // Log the error
    try {
      await systemLogging.logError(
        'SYSTEM',
        1,
        'ERR-2000',
        `System process error: ${error.message}`
      );
    } catch (logError) {
      console.error('Error logging system error:', logError);
    }
  }
}

async function updateNaps() {
  const url = `${app_url}/teams/`;
  try {
    const response = await axios.get(url);
    const data = response.data;

    // Collect teams that need updating instead of updating one by one
    const teamsToUpdate = [];

    for (const a in data) {
      const standardNapRate = 10;
      let house_nap_bonus = 0;
      const owner = data[a].owner_id;
      const team = data[a].team_id;
      const team_status = data[a].status;
      let current_points = data[a].nap_current;
      const total_points = data[a].nap_total;
      const assigned_house = (data[a].data.house !== 'None') ? true : false;
      if (assigned_house) {
        house_nap_bonus = 15;
      }
      if (team_status === 'Napping') {
        let new_status = 'Napping';
        if (current_points + standardNapRate < total_points) {
          current_points += standardNapRate + house_nap_bonus;
        } else if (current_points + standardNapRate >= total_points) {
          current_points = total_points;
          new_status = 'Ready';
        }

        // Add to batch update array instead of making individual requests
        teamsToUpdate.push({
          team_id: team,
          status: new_status,
          points: current_points
        });
      }
    }

    // Process updates in batches
    if (teamsToUpdate.length > 0) {
      await updateTeamsNapBatch(teamsToUpdate);
    }
  } catch (error) {
    console.log(error);
  }
}

async function updateTeamNap(team_id, status, points) {
  const url = `${app_url}/teams/setnap/` + team_id;
  const change = {
    "nap_current": points,
    "status": status
  };
  const options = {
    headers: {
      "Content-Type": "application/json"
    }
  };
  try {
    await axios.put(url, change, options);
    console.log(`Successfully updated team ${team_id} nap status`);
  } catch (error) {
    console.error(`Error updating team ${team_id} nap status:`, error);
  }
}
 
async function updateTeamsNapBatch(teams) {
  console.log(`Batch updating nap status for ${teams.length} teams`); 
  // Process in smaller batches of 25 to avoid overwhelming the server
  const batchSize = 25;
  for (let i = 0; i < teams.length; i += batchSize) {
    const batch = teams.slice(i, i + batchSize); 
    try { 
      const formattedBatch = batch.map(team => ({
        team_id: team.team_id,
        nap_current: team.points,
        status: team.status
      })); 
      const url = `${app_url}/teams/setnap-batch`;
      const response = await axios.put(
        url,
        { teams: formattedBatch },
        { headers: { "Content-Type": "application/json" } }
      );

      console.log(`Completed batch update for teams ${i+1} to ${Math.min(i+batchSize, teams.length)}: ${response.data}`);
    } catch (error) {
      console.error('Error in batch update:', error); 
      console.log('Falling back to individual updates...');
      for (const team of batch) {
        try {
          await updateTeamNap(team.team_id, team.status, team.points);
        } catch (individualError) {
          console.error(`Failed to update team ${team.team_id}:`, individualError);
        }
      }
    }
  }
}

exports.playerBalances = playerBalances; 
 
(async function() {
  await main();  
  await updateNaps();
})();