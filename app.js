// ORG to do: we need to remove any queries hard coded in controllers
// SECURITY to do: RLS row level security
// The user should only be able to update their wallet's rows
// create, update, delete teams and adventures
// only update rewards and update their player row
// they should NEVER be able to update their logs, delete rewards, delete player or create player

// Load environment variables
if (process.env.NODE_ENV !== 'production') {
require('dotenv').config();
}
// Load required packages and libraries
const express = require("express");
const cors = require("cors");
// const { body } = require("express-validator"); sql attacks prevention code needed
const axios = require("axios");
const rateLimit = require("express-rate-limit");
const {
Api,
JsonRpc
} = require('eosjs');
const {
JsSignatureProvider
} = require('eosjs/dist/eosjs-jssig');
const fetch = require('node-fetch');
const {
TextDecoder,
TextEncoder
} = require('util');

const playerRoutes = require("./src/routes/player.js");
const houseRoutes = require("./src/routes/houses.js");
const teamRoutes = require("./src/routes/teams.js");
const adventureRoutes = require("./src/routes/adventures.js");
const rewardRoutes = require("./src/routes/rewards.js");
const sessionRoutes = require("./src/routes/sessions.js");
const adminDashboardRoutes = require("./src/routes/admin/dashboard.js");
const globalLimitRoutes = require("./src/routes/logs/global-limit.js");
const playerLimitRoutes = require("./src/routes/logs/player-limit.js");
const systemLogsRoutes = require("./src/routes/logs/system-logs.js");
const allController = require('./src/controls/controller.js');

const {
Pool
} = require('pg');

// Define constants
const delaySeconds = 10;
const delayMillis = delaySeconds * 1000;
const privateKeys = [process.env.MYKEY];
const mydbkey = process.env.DATABASE_URL;
const rpc = new JsonRpc("https://wax.cryptolions.io", {
fetch
});
const signatureProvider = new JsSignatureProvider(privateKeys);
const api = new Api({
rpc,
signatureProvider,
textDecoder: new TextDecoder(),
textEncoder: new TextEncoder()
});

// Create rate limiter
const apiLimiter = rateLimit({
windowMs: delayMillis,
max: 500,
message: "You have exceeded the maximum number of requests. Please try again later.",
headers: true,
onLimitReached: (req, res, options) => {
console.log("Rate limit reached for ${req.ip} at ${new Date()}");
}
});

const app = express();

// CORS configuration - MUST be first middleware
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      "http://localhost:3000", // Development frontend 
      "https://react-crushies-lime.vercel.app", // Production frontend on Vercel
      "https://express-crushie.herokuapp.com", // Your Heroku app
    ];
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "wax-user-account", "Origin", "X-Requested-With", "Accept"],
  optionsSuccessStatus: 200 // This is important - some browsers choke on 204
}));

// Handle preflight requests explicitly
app.options('*', cors());

app.use(apiLimiter);
app.use(express.static("public"));
app.use(express.json({ limit: '1mb' }));

// app.use("/sessions", sessionRoutes);
// app.use("/players", allController.getUserSessionById, playerRoutes);
// app.use("/houses", allController.getUserSessionById, houseRoutes);
// app.use("/teams", allController.getUserSessionById, teamRoutes);
// app.use("/adventures", allController.getUserSessionById, adventureRoutes);
// app.use("/rewards", allController.getUserSessionById, rewardRoutes);

// Import the auth middleware
const { sessionAuth } = require('./src/middleware/authMiddleware');

app.use("/sessions", sessionRoutes);

// Use the regular player routes for all requests
app.use("/players", playerRoutes);

// Add a specific middleware for the player update endpoint
// This is a more targeted approach that doesn't interfere with other routes
app.use((req, res, next) => {
  // Only apply to PUT requests to /players/[wax_id] where wax_id contains a dot (.)
  if (req.method === 'PUT' &&
      req.path.startsWith('/players/') &&
      !req.path.includes('/zones/') &&
      !req.path.includes('/teams/') &&
      !req.path.includes('/credits/') &&
      !req.path.includes('/nectar/') &&
      !req.path.includes('/xp/') &&
      !req.path.includes('/gxp/') &&
      !req.path.includes('/lv/') &&
      req.path.includes('.')) {

    console.log('Detected player update request:', req.path);

    // Extract the WAX ID from the path
    const waxId = req.path.split('/')[2];
    console.log('Extracted WAX ID:', waxId);

    // Set the WAX ID in the request params
    req.params = req.params || {};
    req.params.id = waxId;

    console.log('Using direct player controller instead of middleware');
    // Skip the middleware and use the controller directly
    return allController.updatePlayer(req, res);
  }

  next();
});

app.use("/houses", houseRoutes);
app.use("/teams", teamRoutes);
app.use("/adventures", adventureRoutes);
app.use("/rewards", rewardRoutes);

// Admin routes
app.use("/admin", adminDashboardRoutes);
app.use("/global-limits", globalLimitRoutes);
app.use("/player-limits", playerLimitRoutes);
app.use("/system-logs", systemLogsRoutes);

// Define an error handler
app.use((error, req, res, next) => {
return res.status(500).json({
error: error.toString()
});
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
console.log("The application is online .... PORT: " + PORT);
});
