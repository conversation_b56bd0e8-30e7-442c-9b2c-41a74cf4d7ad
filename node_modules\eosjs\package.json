{"name": "e<PERSON><PERSON><PERSON>", "version": "22.1.0", "description": "Talk to eos API", "main": "dist/index.js", "scripts": {"cypress": "cypress run --spec 'cypress/integration/index.spec.js'", "cypress-ui": "cypress open", "prepare": "npm run build", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "test": "jest src/tests/*eosjs*", "test-node": "jest src/tests/*node*", "test-types": "jest src/tests/type-checks.test.ts", "test-all": "yarn test && yarn test-node && yarn test-types && yarn cypress", "build": "rimraf dist && tsc -p ./tsconfig.json && node scripts/copy-ripe-md.js", "build-web": "webpack --config webpack.prod.js && webpack --config webpack.debug.js", "build-production": "yarn build && yarn build-web && yarn test-all", "docs-init": "sh .docs/scripts/init.sh", "docs-build": "sh .docs/scripts/build.sh", "docs-serve": "python -m SimpleHTTPServer", "docs-publish": "sh .docs/scripts/publish.sh"}, "author": "block.one", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/EOSIO/eosjs.git"}, "dependencies": {"bn.js": "5.2.0", "elliptic": "6.5.4", "hash.js": "1.1.7", "pako": "2.0.3"}, "devDependencies": {"@blockone/eslint-config-blockone": "^4.0.1", "@cypress/skip-test": "^2.6.1", "@types/elliptic": "^6.4.13", "@types/jest": "^26.0.24", "@types/node": "^14.17.5", "@types/node-fetch": "^2.5.11", "@types/pako": "^1.0.2", "buffer": "^6.0.3", "clean-webpack-plugin": "^3.0.0", "crypto-browserify": "^3.12.0", "cypress": "^7.7.0", "eosjs-ecc": "^4.0.7", "eslint": "^7.30.0", "jest": "^26.6.3", "jest-extended": "^0.11.5", "jest-fetch-mock": "^3.0.3", "rimraf": "^3.0.2", "ts-jest": "^26.5.6", "ts-loader": "^9.2.3", "typescript": "^4.3.5", "webpack": "^5.44.0", "webpack-cli": "^4.7.2"}, "jest": {"automock": false, "setupFiles": ["./src/tests/setupJest.js"], "setupFilesAfterEnv": ["jest-extended"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"^.+\\.(tsx?)$": "ts-jest"}, "globals": {"ts-jest": {"tsconfig": "tsconfig.json"}}, "testRegex": "(/src/.*(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "testEnvironment": "node"}}