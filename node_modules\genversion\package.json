{"name": "genversion", "version": "3.2.0", "description": "A command line utility to read version from package.json and attach it into your module as a property", "keywords": ["release", "version", "tag", "build", "check", "security", "automation", "semver", "module", "package", "development", "dev", "generator", "frontend", "bundle", "cli", "es6", "es2015"], "homepage": "https://github.com/axelpale/genversion", "main": "index.js", "bin": {"genversion": "bin/genversion.js"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/axelpale/genversion.git"}, "license": "MIT", "dependencies": {"commander": "^7.2.0", "ejs": "^3.1.9", "find-package": "^1.0.0"}, "devDependencies": {"fs-extra": "^10.0.1", "mocha": "^8.4.0", "should": "^13.1.0", "standard": "^16.0.4"}, "engines": {"node": ">=10.0.0"}, "scripts": {"test": "npm run lint && mocha", "lint": "standard", "lintfix": "standard --fix", "gv": "./bin/genversion.js lib/version.js", "check": "./bin/genversion.js --verbose --check-only lib/version.js", "release": "npm run gv && npm run test && npm publish"}}