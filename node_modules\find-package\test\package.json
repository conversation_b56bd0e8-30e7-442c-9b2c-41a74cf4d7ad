{"name": "find-package", "version": "1.0.0", "description": "Find the nearest package.json in your current node module", "main": "index.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "mocha ./**/*.spec.js"}, "repository": {"type": "git", "url": "https://github.com/jalba/find-package.git"}, "keywords": ["package.json", "find", "directory"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jalba/find-package/issues"}, "homepage": "https://github.com/jalba/find-package", "dependencies": {"parents": "^1.0.1"}, "devDependencies": {"chai": "^2.3.0", "mocha": "^2.2.5"}}