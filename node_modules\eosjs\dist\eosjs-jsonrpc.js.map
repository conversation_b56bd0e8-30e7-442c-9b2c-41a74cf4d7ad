{"version": 3, "file": "eosjs-jsonrpc.js", "sourceRoot": "", "sources": ["../src/eosjs-jsonrpc.ts"], "names": [], "mappings": ";AAAA;;GAEG;AACH,yCAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGzC,iDAA0E;AAkC1E,mDAA4C;AAE5C,IAAM,UAAU,GAAG,UAAC,IAAgB;;IAChC,IAAI,MAAM,GAAG,EAAE,CAAC;;QAChB,KAAgB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;YAAjB,IAAM,CAAC,iBAAA;YACR,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C;;;;;;;;;IACD,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,qBAAqB;AACrB;IAII;;;;;OAKG;IACH,iBACI,QAAgB,EAChB,IAEM;QAFN,qBAAA,EAAA,SAEM;QAEN,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;aAAM;YACH,IAAI,CAAC,YAAY,GAAI,MAAc,CAAC,KAAK,CAAC;SAC7C;IACL,CAAC;IAED,wGAAwG;IAC3F,uBAAK,GAAlB,UAAmB,IAAY,EAAE,IAAS;;;;;;;wBAI5B,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;wBACjB,qBAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,EAAE;gCACrC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gCAC1B,MAAM,EAAE,MAAM;6BACjB,CAAC,EAAA;;wBAHF,QAAQ,GAAG,SAGT,CAAC;wBACI,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAA5B,IAAI,GAAG,SAAqB,CAAC;wBAC7B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;4BACzC,MAAM,IAAI,yBAAQ,CAAC,IAAI,CAAC,CAAC;yBAC5B;6BAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;4BAC1C,MAAM,IAAI,yBAAQ,CAAC,IAAI,CAAC,CAAC;yBAC5B;;;;wBAED,GAAC,CAAC,YAAY,GAAG,IAAI,CAAC;wBACtB,MAAM,GAAC,CAAC;;wBAEZ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;4BACd,MAAM,IAAI,yBAAQ,CAAC,IAAI,CAAC,CAAC;yBAC5B;wBACD,sBAAO,IAAI,EAAC;;;;KACf;IAEY,iCAAe,GAA5B,UACI,IAAY,EACZ,MAAc,EACd,OAAe;;;;4BAER,qBAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;4BAA/E,sBAAO,SAAwE,EAAC;;;;KACnF;IAEY,iCAAe,GAA5B,UACI,IAAY,EACZ,MAAc,EACd,IAAW;;;;4BAEJ,qBAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,IAAI,MAAA,EAAE,CAAC,EAAA;4BAA5E,sBAAO,SAAqE,EAAC;;;;KAChF;IAED,sCAAsC;IACzB,yBAAO,GAApB,UAAqB,WAAmB;;;;4BAC7B,qBAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,EAAA;4BAA3E,sBAAO,SAAoE,EAAC;;;;KAC/E;IAED,0CAA0C;IAC7B,6BAAW,GAAxB,UAAyB,WAAmB;;;;4BACjC,qBAAM,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,EAAA;4BAA/E,sBAAO,SAAwE,EAAC;;;;KACnF;IAED,0DAA0D;IAC7C,6CAA2B,GAAxC,UAAyC,QAAyB,EAAE,IAAc;;;;4BACvE,qBAAM,IAAI,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,QAAQ,UAAA,EAAE,IAAI,MAAA,EAAE,CAAC,EAAA;4BAApF,sBAAO,SAA6E,EAAC;;;;KACxF;IAED,oDAAoD;IACvC,iDAA+B,GAA5C,UAA6C,EAMR;YALjC,aAAU,EAAV,KAAK,mBAAG,EAAE,KAAA,EACV,2BAA2B,EAA3B,mBAAmB,mBAAG,KAAK,KAAA,EAC3B,eAAe,EAAf,OAAO,mBAAG,KAAK,KAAA,EACf,mBAAkB,EAAlB,WAAW,mBAAG,IAAI,KAAA,EAClB,mBAAkB,EAAlB,WAAW,mBAAG,IAAI,KAAA;;;;4BAEX,qBAAM,IAAI,CAAC,KAAK,CAAC,2CAA2C,EAAE,EAAE,WAAW,aAAA,EAAE,WAAW,aAAA,EAAE,KAAK,OAAA,EAAE,mBAAmB,qBAAA,EAAE,OAAO,SAAA,EAAE,CAAC,EAAA;4BAAvI,sBAAO,SAAgI,EAAC;;;;KAC3I;IAED,qDAAqD;IACxC,wCAAsB,GAAnC,UAAoC,YAA6B;;;;4BACtD,qBAAM,IAAI,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,eAAe,EAAE,YAAY,EAAE,CAAC,EAAA;4BAA9F,sBAAO,SAAuF,EAAC;;;;KAClG;IAED,6CAA6C;IAChC,gCAAc,GAA3B,UAA4B,QAAgB;;;;4BACjC,qBAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAA;4BAA5E,sBAAO,SAAqE,EAAC;;;;KAChF;IAED,wCAAwC;IAC3B,2BAAS,GAAtB,UAAuB,YAA6B;;;;4BACzC,qBAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,EAAE,eAAe,EAAE,YAAY,EAAE,CAAC,EAAA;4BAAjF,sBAAO,SAA0E,EAAC;;;;KACrF;IAED,uCAAuC;IAC1B,0BAAQ,GAArB,UAAsB,WAAmB;;;;4BAC9B,qBAAM,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;4BAC1C,YAAY,EAAE,WAAW;4BACzB,YAAY,EAAE,IAAI;yBACrB,CAAC,EAAA;4BAHF,sBAAO,SAGL,EAAC;;;;KACN;IAED,4CAA4C;IAC/B,+BAAa,GAA1B,UAA2B,WAAmB;;;;4BACnC,qBAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,EAAA;4BAAjF,sBAAO,SAA0E,EAAC;;;;KACrF;IAED,mDAAmD;IACtC,sCAAoB,GAAjC,UAAkC,IAAY,EAAE,OAAe,EAAE,MAAqB;QAArB,uBAAA,EAAA,aAAqB;;;;4BAC3E,qBAAM,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;4BAApF,sBAAO,SAA6E,EAAC;;;;KACxF;IAED,iDAAiD;IACpC,oCAAkB,GAA/B,UAAgC,IAAY,EAAE,MAAc;;;;4BACjD,qBAAM,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,IAAI,MAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;4BAAzE,sBAAO,SAAkE,EAAC;;;;KAC7E;IAED,uCAAuC;IAC1B,0BAAQ,GAArB;;;;4BACW,qBAAM,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,CAAC,EAAA;4BAAjD,sBAAO,SAA0C,EAAC;;;;KACrD;IAED,oDAAoD;IACvC,uCAAqB,GAAlC;;;;4BACW,qBAAM,IAAI,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,CAAC,EAAA;4BAA9D,sBAAO,SAAuD,EAAC;;;;KAClE;IAED,4CAA4C;IAC/B,+BAAa,GAA1B,UAA2B,IAAW,EAAE,UAAe,EAAE,KAAU;QAAxC,qBAAA,EAAA,WAAW;QAAE,2BAAA,EAAA,eAAe;QAAE,sBAAA,EAAA,UAAU;;;;4BACxD,qBAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,IAAI,MAAA,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,OAAA,EAAE,CAAC,EAAA;4BAA5F,sBAAO,SAAqF,EAAC;;;;KAChG;IAED,mDAAmD;IACtC,sCAAoB,GAAjC,UAAkC,WAAmB;;;;4BAC1C,qBAAM,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,EAAA;4BAAxF,sBAAO,SAAiF,EAAC;;;;KAC5F;IAED,kFAAkF;IACrE,2BAAS,GAAtB,UAAuB,WAAmB;;;;;4BACvB,qBAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,EAAA;;wBAA5C,MAAM,GAAG,SAAmC;wBAC5C,GAAG,GAAG,8BAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBACvC,sBAAO,EAAE,WAAW,EAAE,MAAM,CAAC,YAAY,EAAE,GAAG,KAAA,EAAE,EAAC;;;;KACpD;IAED,0CAA0C;IAC7B,6BAAW,GAAxB,UAAyB,WAAmB;;;;4BACjC,qBAAM,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,CAAC,EAAA;4BAA/E,sBAAO,SAAwE,EAAC;;;;KACnF;IAED,yDAAyD;IAC5C,4CAA0B,GAAvC,UAAwC,IAAW,EAAE,UAAe,EAAE,KAAU;QAAxC,qBAAA,EAAA,WAAW;QAAE,2BAAA,EAAA,eAAe;QAAE,sBAAA,EAAA,UAAU;;;;4BACrE,qBAAM,IAAI,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,IAAI,MAAA,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,OAAA,EAAE,CAAC,EAAA;4BAAzG,sBAAO,SAAkG,EAAC;;;;KAC7G;IAED,6CAA6C;IAChC,gCAAc,GAA3B,UAA4B,EAYtB;YAXF,YAAW,EAAX,IAAI,mBAAG,IAAI,KAAA,EACX,IAAI,UAAA,EACJ,KAAK,WAAA,EACL,KAAK,WAAA,EACL,mBAAgB,EAAhB,WAAW,mBAAG,EAAE,KAAA,EAChB,mBAAgB,EAAhB,WAAW,mBAAG,EAAE,KAAA,EAChB,sBAAkB,EAAlB,cAAc,mBAAG,CAAC,KAAA,EAClB,gBAAa,EAAb,QAAQ,mBAAG,EAAE,KAAA,EACb,aAAU,EAAV,KAAK,mBAAG,EAAE,KAAA,EACV,eAAe,EAAf,OAAO,mBAAG,KAAK,KAAA,EACf,kBAAkB,EAAlB,UAAU,mBAAG,KAAK,KAAA;;;;4BAEX,qBAAM,IAAI,CAAC,KAAK,CACnB,0BAA0B,EAAE;4BACxB,IAAI,MAAA;4BACJ,IAAI,MAAA;4BACJ,KAAK,OAAA;4BACL,KAAK,OAAA;4BACL,WAAW,aAAA;4BACX,WAAW,aAAA;4BACX,cAAc,gBAAA;4BACd,QAAQ,UAAA;4BACR,KAAK,OAAA;4BACL,OAAO,SAAA;4BACP,UAAU,YAAA;yBACb,CAAC,EAAA;4BAbN,sBAAO,SAaD,EAAC;;;;KACV;IAED,gDAAgD;IACnC,mCAAiB,GAA9B,UAA+B,EAYzB;YAXF,YAAW,EAAX,IAAI,mBAAG,IAAI,KAAA,EACX,IAAI,UAAA,EACJ,KAAK,WAAA,EACL,UAAU,gBAAA,EACV,mBAAqB,EAArB,WAAW,mBAAG,OAAO,KAAA,EACrB,WAAW,iBAAA,EACX,WAAW,iBAAA,EACX,WAAW,iBAAA,EACX,aAAU,EAAV,KAAK,mBAAG,EAAE,KAAA,EACV,eAAe,EAAf,OAAO,mBAAG,KAAK,KAAA,EACf,kBAAkB,EAAlB,UAAU,mBAAG,KAAK,KAAA;;;;4BAEX,qBAAM,IAAI,CAAC,KAAK,CACnB,6BAA6B,EAAE;4BAC3B,IAAI,MAAA;4BACJ,IAAI,MAAA;4BACJ,KAAK,OAAA;4BACL,UAAU,YAAA;4BACV,WAAW,aAAA;4BACX,WAAW,aAAA;4BACX,WAAW,aAAA;4BACX,WAAW,aAAA;4BACX,KAAK,OAAA;4BACL,OAAO,SAAA;4BACP,UAAU,YAAA;yBACb,CAAC,EAAA;4BAbN,sBAAO,SAaD,EAAC;;;;KACV;IAED,iDAAiD;IACpC,oCAAkB,GAA/B,UAAgC,EAM1B;YALF,IAAI,UAAA,EACJ,KAAK,WAAA,EACL,mBAAgB,EAAhB,WAAW,mBAAG,EAAE,KAAA,EAChB,mBAAgB,EAAhB,WAAW,mBAAG,EAAE,KAAA,EAChB,aAAU,EAAV,KAAK,mBAAG,EAAE,KAAA;;;;4BAEH,qBAAM,IAAI,CAAC,KAAK,CACnB,8BAA8B,EAAE;4BAC5B,IAAI,MAAA;4BACJ,KAAK,OAAA;4BACL,WAAW,aAAA;4BACX,WAAW,aAAA;4BACX,KAAK,OAAA;yBACR,CAAC,EAAA;4BAPN,sBAAO,SAOD,EAAC;;;;KACV;IAED,gHAAgH;IACnG,iCAAe,GAA5B,UAA6B,IAA2B;;;;;;wBAC7C,KAAA,uCAAuB,CAAA;wBAAE,qBAAM,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE;gCAC5E,WAAW,EAAE,IAAI,CAAC,WAAW;gCAC7B,cAAc,EAAE,IAAI,CAAC,aAAa;6BACrC,CAAC,EAAA;4BAHF,sBAAO,kBAAwB,CAAC,SAG9B,CAAC,CAAC,aAAa,EAAC,EAAC;;;;KACtB;IAED,oGAAoG;IACvF,kCAAgB,GAA7B,UACI,EAAsG;YAApG,UAAU,gBAAA,EAAE,mBAAe,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,qBAAqB,2BAAA,EAAE,yBAAyB,+BAAA;;;;4BAExE,qBAAM,IAAI,CAAC,KAAK,CAAC,4BAA4B,EAAE;4BAClD,UAAU,YAAA;4BACV,WAAW,aAAA;4BACX,wBAAwB,EAAE,UAAU,CAAC,yBAAyB,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;4BACpF,UAAU,EAAE,UAAU,CAAC,qBAAqB,CAAC;yBAChD,CAAC,EAAA;4BALF,sBAAO,SAKL,EAAC;;;;KACN;IAED,iDAAiD;IACpC,qCAAmB,GAAhC,UAAiC,EAA2E,EACxG,mBAAoC;YADL,UAAU,gBAAA,EAAE,mBAAe,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,qBAAqB,2BAAA;QACjF,oCAAA,EAAA,2BAAoC;;;;4BAC7B,qBAAM,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE;4BACrD,WAAW,EAAE;gCACT,UAAU,YAAA;gCACV,WAAW,aAAA;gCACX,wBAAwB,EAAE,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;gCACvD,UAAU,EAAE,UAAU,CAAC,qBAAqB,CAAC;6BAChD;4BACD,qBAAqB,EAAE,mBAAmB;yBAC7C,CAAC,EAAA;4BARF,sBAAO,SAQL,EAAC;;;;KACN;IAEY,mCAAiB,GAA9B,UAA+B,YAAmC;;;;;;wBACxD,UAAU,GAAgB,YAAY,CAAC,GAAG,CAAC,UAAC,EAAqG;gCAApG,UAAU,gBAAA,EAAE,mBAAe,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,qBAAqB,2BAAA,EAAE,yBAAyB,+BAAA;4BAC5H,OAAO;gCACH,UAAU,YAAA;gCACV,WAAW,aAAA;gCACX,wBAAwB,EAAE,UAAU,CAAC,yBAAyB,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;gCACpF,UAAU,EAAE,UAAU,CAAC,qBAAqB,CAAC;6BAChD,CAAC;wBACN,CAAC,CAAC,CAAC;wBACI,qBAAM,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,UAAU,CAAE,EAAA;4BAAnE,sBAAO,SAA4D,EAAC;;;;KACvE;IAED,oCAAoC;IACvB,kCAAgB,GAA7B,UACI,EAAsG;YAApG,UAAU,gBAAA,EAAE,mBAAe,EAAf,WAAW,mBAAG,CAAC,KAAA,EAAE,qBAAqB,2BAAA,EAAE,yBAAyB,+BAAA;;;;4BAExE,qBAAM,IAAI,CAAC,KAAK,CAAC,4BAA4B,EAAE;4BAClD,UAAU,YAAA;4BACV,WAAW,aAAA;4BACX,wBAAwB,EAAE,UAAU,CAAC,yBAAyB,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;4BACpF,UAAU,EAAE,UAAU,CAAC,qBAAqB,CAAC;yBAChD,CAAC,EAAA;4BALF,sBAAO,SAKL,EAAC;;;;KACN;IAED,oCAAoC;IACvB,6BAAW,GAAxB;;;wBAA8D,qBAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAA;wBAA9C,sBAAO,SAAuC,EAAC;;;KAAE;IAExG,4CAA4C;IAC/B,iCAAe,GAA5B,UAA6B,SAAiB;;;;4BACnC,qBAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,SAAS,WAAA,EAAE,CAAC,EAAA;4BAAjE,sBAAO,SAA0D,EAAC;;;;KACrE;IAED,4CAA4C;IAC/B,qCAAmB,GAAhC,UAAiC,WAAmB,EAAE,GAAkB,EAAE,MAAqB;QAAzC,oBAAA,EAAA,UAAkB;QAAE,uBAAA,EAAA,aAAqB;;;;4BACpF,qBAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,KAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;4BAA9F,sBAAO,SAAuF,EAAC;;;;KAClG;IAED,gDAAgD;IACnC,yCAAuB,GAApC,UAAqC,EAAU,EAAE,YAA2B;QAA3B,6BAAA,EAAA,mBAA2B;;;;4BACjE,qBAAM,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,EAAE,IAAA,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC,EAAA;4BAA5F,sBAAO,SAAqF,EAAC;;;;KAChG;IAED,iDAAiD;IACpC,0CAAwB,GAArC,UAAsC,SAAiB;;;;4BAC5C,qBAAM,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,EAAA;4BAAlF,sBAAO,SAA2E,EAAC;;;;KACtF;IAED,wDAAwD;IAC3C,iDAA+B,GAA5C,UAA6C,kBAA0B;;;;4BAC5D,qBAAM,IAAI,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,CAAC,EAAA;4BAA3G,sBAAO,SAAoG,EAAC;;;;KAC/G;IACL,cAAC;AAAD,CAAC,AA1UD,IA0UC,CAAC,UAAU;AA1UC,0BAAO"}