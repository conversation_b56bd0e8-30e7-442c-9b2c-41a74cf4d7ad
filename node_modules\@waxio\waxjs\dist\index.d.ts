import { Api, JsonRpc } from "eosjs";
import { SignatureProvider, Transaction } from "eosjs/dist/eosjs-api-interfaces";
import { ILoginResponse } from "./interfaces";
export declare class WaxJS {
    readonly rpc: JsonRpc;
    api: Api;
    user?: ILoginResponse;
    private signingApi;
    private readonly apiSigner;
    private readonly waxSigningURL;
    private readonly waxAutoSigningURL;
    private readonly eosApiArgs;
    private readonly freeBandwidth;
    private readonly feeFallback;
    private readonly metricURL;
    private readonly returnTempAccounts;
    private readonly verifyTx;
    get userAccount(): string;
    get pubKeys(): string[];
    get isTemp(): boolean;
    get createInfo(): any;
    get avatar(): string;
    get trustScore(): number;
    get trustScoreProvider(): string;
    get version(): string;
    get proofVerified(): boolean;
    constructor({ rpcEndpoint, tryAutoLogin, userAccount, pubKeys, apiSigner, waxSigningURL, waxAutoSigningURL, eosApiArgs, freeBandwidth, feeFallback, verifyTx, metricURL, returnTempAccounts, }: {
        rpcEndpoint: string;
        userAccount?: string;
        pubKeys?: string[];
        tryAutoLogin?: boolean;
        apiSigner?: SignatureProvider;
        waxSigningURL?: string;
        waxAutoSigningURL?: string;
        eosApiArgs?: any;
        freeBandwidth?: boolean;
        feeFallback?: boolean;
        createData?: any;
        verifyTx?: (user: ILoginResponse, originalTx: Transaction, augmentedTx: Transaction) => void;
        metricURL?: string;
        returnTempAccounts?: boolean;
    });
    login(nonce?: string): Promise<string>;
    isAutoLoginAvailable(): Promise<boolean>;
    logout(): Promise<void>;
    userAccountProof(nonce: string, description: string, verify?: boolean): Promise<any>;
    waxProof(nonce: string, verify?: boolean): Promise<any>;
    private receiveLogin;
}
export declare function defaultTxVerifier(user: ILoginResponse, originalTx: Transaction, augmentedTx: Transaction, maxPayment?: number): void;
