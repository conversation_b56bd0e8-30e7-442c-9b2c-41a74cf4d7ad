{"name": "@waxio/waxjs", "version": "1.7.1", "description": "Javascript API for integration with the WAX Cloud Wallet.", "scripts": {"build": "npm run genversion && tsc", "build-web": "npm run genversion && webpack --config webpack.prod.js", "prettier": "prettier --write \"src/**/*.ts\" \"src/**/*.js\" \"test/**/*.ts\" \"test/**/*.js\"", "lint": "tslint -p tsconfig.json", "test": "npm run lint && mocha -r ts-node/register test/**/*.test.ts", "docs": "typedoc --excludePrivate --exclude src/waxEventSource.ts --out docs src", "docs:serve": "serve -s docs", "serve": "serve -s dist-web", "prepare": "npm run build", "prepublishOnly": "npm run lint && npm run build && npm run build-web", "preversion": "npm run lint", "version": "npm run prettier && git add -A src", "postversion": "git push && git push --tags", "genversion": "genversion src/version.ts -e"}, "author": "WAX", "homepage": "https://github.com/worldwide-asset-exchange/waxjs#readme", "license": "MIT", "devDependencies": {"@types/chai": "^4.3.4", "@types/mocha": "^5.2.7", "@types/node": "^12.20.55", "chai": "^4.3.7", "expect-puppeteer": "^5.0.4", "mocha": "^10.2.0", "prettier": "^1.19.1", "puppeteer": "^10.4.0", "serve": "^11.3.2", "ts-loader": "^8.4.0", "ts-node": "^10.9.1", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "typedoc": "^0.15.8", "typescript": "^4.9.5", "webpack": "^4.46.0", "webpack-cli": "^4.10.0"}, "dependencies": {"eosjs": "^22.1.0", "genversion": "^3.1.1", "ts-node-dev": "^1.1.8"}, "files": ["dist"]}