{"title": "JSON schema for the TypeScript compiler's configuration file", "$schema": "http://json-schema.org/draft-04/schema#", "id": "https://json.schemastore.org/tsconfig", "definitions": {"//": {"explainer": "https://www.typescriptlang.org/docs/handbook/tsconfig-json.html#overview", "reference": "https://www.typescriptlang.org/tsconfig", "reference metadata": "https://github.com/microsoft/TypeScript-Website/blob/v2/packages/tsconfig-reference/scripts/tsconfigRules.ts"}, "filesDefinition": {"properties": {"files": {"description": "If no 'files' or 'include' property is present in a tsconfig.json, the compiler defaults to including all files in the containing directory and subdirectories except those specified by 'exclude'. When a 'files' property is specified, only those files and those specified by 'include' are included.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "excludeDefinition": {"properties": {"exclude": {"description": "Specifies a list of files to be excluded from compilation. The 'exclude' property only affects the files included via the 'include' property and not the 'files' property. Glob patterns require TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "includeDefinition": {"properties": {"include": {"description": "Specifies a list of glob patterns that match files to be included in compilation. If no 'files' or 'include' property is present in a tsconfig.json, the compiler defaults to including all files in the containing directory and subdirectories except those specified by 'exclude'. Requires TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}}}, "compileOnSaveDefinition": {"properties": {"compileOnSave": {"description": "Enable Compile-on-Save for this project.", "type": "boolean"}}}, "extendsDefinition": {"properties": {"extends": {"description": "Path to base configuration file to inherit from. Requires TypeScript version 2.1 or later.", "type": "string"}}}, "compilerOptionsDefinition": {"properties": {"compilerOptions": {"type": "object", "description": "Instructs the TypeScript compiler how to compile .ts files.", "properties": {"charset": {"description": "The character set of the input files. This setting is deprecated.", "type": "string"}, "composite": {"description": "Enables building for project references. Requires TypeScript version 3.0 or later.", "type": "boolean", "default": true}, "declaration": {"description": "Generates corresponding d.ts files.", "type": "boolean", "default": false}, "declarationDir": {"description": "Specify output directory for generated declaration files. Requires TypeScript version 2.0 or later.", "type": ["string", "null"]}, "diagnostics": {"description": "Show diagnostic information. This setting is deprecated. See `extendedDiagnostics.`", "type": "boolean"}, "disableReferencedProjectLoad": {"description": "Recommend IDE's to load referenced composite projects dynamically instead of loading them all immediately. Requires TypeScript version 4.0 or later.", "type": "boolean"}, "emitBOM": {"description": "Emit a UTF-8 Byte Order Mark (BOM) in the beginning of output files.", "type": "boolean", "default": false}, "emitDeclarationOnly": {"description": "Only emit '.d.ts' declaration files. Requires TypeScript version 2.8 or later.", "type": "boolean", "default": false}, "incremental": {"description": "Enable incremental compilation. Requires TypeScript version 3.4 or later.", "type": "boolean"}, "tsBuildInfoFile": {"description": "Specify file to store incremental compilation information. Requires TypeScript version 3.4 or later.", "default": ".tsbuildinfo", "type": "string"}, "inlineSourceMap": {"description": "Emit a single file with source maps instead of having a separate file. Requires TypeScript version 1.5 or later.", "type": "boolean", "default": false}, "inlineSources": {"description": "Emit the source alongside the sourcemaps within a single file; requires --inlineSourceMap to be set. Requires TypeScript version 1.5 or later.", "type": "boolean", "default": false}, "jsx": {"description": "Specify JSX code generation: 'preserve', 'react', 'react-jsx', 'react-jsxdev' or'react-native'. Requires TypeScript version 2.2 or later.", "enum": ["preserve", "react", "react-jsx", "react-jsxdev", "react-native"]}, "reactNamespace": {"description": "Specify the object invoked for createElement and __spread when targeting 'react' JSX emit.", "type": "string", "default": "React"}, "jsxFactory": {"description": "Specify the JSX factory function to use when targeting react JSX emit, e.g. 'React.createElement' or 'h'. Requires TypeScript version 2.1 or later.", "type": "string", "default": "React.createElement"}, "jsxFragmentFactory": {"description": "Specify the JSX Fragment reference to use for fragements when targeting react JSX emit, e.g. 'React.Fragment' or 'Fragment'. Requires TypeScript version 4.0 or later.", "type": "string", "default": "React.Fragment"}, "jsxImportSource": {"description": "Declare the module specifier to be used for importing the `jsx` and `jsxs` factory functions when using jsx as \"react-jsx\" or \"react-jsxdev\". Requires TypeScript version 4.1 or later.", "type": "string", "default": "react"}, "listFiles": {"description": "Print names of files part of the compilation.", "type": "boolean", "default": false}, "mapRoot": {"description": "Specify the location where debugger should locate map files instead of generated locations", "type": "string"}, "module": {"description": "Specify module code generation: 'None', 'CommonJS', 'AMD', 'System', 'UMD', 'ES6', 'ES2015', 'ES2020' or 'ESNext'. Only 'AMD' and 'System' can be used in conjunction with --outFile.", "type": "string", "anyOf": [{"enum": ["CommonJS", "AMD", "System", "UMD", "ES6", "ES2015", "ES2020", "ESNext", "None"]}, {"pattern": "^([Cc][Oo][Mm][Mm][Oo][Nn][Jj][Ss]|[AaUu][Mm][Dd]|[Ss][Yy][Ss][Tt][Ee][Mm]|[Ee][Ss]([356]|201[567]|2020|[Nn][Ee][Xx][Tt])|[Nn][Oo][Nn][Ee])$"}]}, "moduleResolution": {"description": "Specifies module resolution strategy: 'node' (Node) or 'classic' (TypeScript pre 1.6) .", "type": "string", "anyOf": [{"enum": ["Classic", "Node"]}, {"pattern": "^(([Nn]ode)|([Cc]lassic))$"}], "default": "classic"}, "newLine": {"description": "Specifies the end of line sequence to be used when emitting files: 'crlf' (Windows) or 'lf' (Unix). Requires TypeScript version 1.5 or later.", "type": "string", "anyOf": [{"enum": ["crlf", "lf"]}, {"pattern": "^(CRLF|LF|crlf|lf)$"}]}, "noEmit": {"description": "Do not emit output.", "type": "boolean", "default": false}, "noEmitHelpers": {"description": "Do not generate custom helper functions like __extends in compiled output. Requires TypeScript version 1.5 or later.", "type": "boolean", "default": false}, "noEmitOnError": {"description": "Do not emit outputs if any type checking errors were reported. Requires TypeScript version 1.4 or later.", "type": "boolean", "default": false}, "noImplicitAny": {"description": "Warn on expressions and declarations with an implied 'any' type. Enabling this setting is recommended.", "type": "boolean"}, "noImplicitThis": {"description": "Raise error on 'this' expressions with an implied any type. Enabling this setting is recommended. Requires TypeScript version 2.0 or later.", "type": "boolean"}, "noUnusedLocals": {"description": "Report errors on unused locals. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "noUnusedParameters": {"description": "Report errors on unused parameters. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "noLib": {"description": "Do not include the default library file (lib.d.ts).", "type": "boolean", "default": false}, "noResolve": {"description": "Do not add triple-slash references or module import targets to the list of compiled files.", "type": "boolean", "default": false}, "noStrictGenericChecks": {"description": "Disable strict checking of generic signatures in function types. Requires TypeScript version 2.4 or later.", "type": "boolean", "default": false}, "skipDefaultLibCheck": {"description": "Use `skipLibCheck` instead. Skip type checking of default library declaration files.", "type": "boolean", "default": false}, "skipLibCheck": {"description": "Skip type checking of declaration files. Enabling this setting is recommended. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "outFile": {"description": "Concatenate and emit output to single file.", "type": "string"}, "outDir": {"description": "Redirect output structure to the directory.", "type": "string"}, "preserveConstEnums": {"description": "Do not erase const enum declarations in generated code.", "type": "boolean", "default": false}, "preserveSymlinks": {"description": "Do not resolve symlinks to their real path; treat a symlinked file like a real one.", "type": "boolean", "default": false}, "preserveWatchOutput": {"description": "Keep outdated console output in watch mode instead of clearing the screen.", "type": "boolean"}, "pretty": {"description": "Stylize errors and messages using color and context (experimental).", "type": "boolean", "default": true}, "removeComments": {"description": "Do not emit comments to output.", "type": "boolean", "default": false}, "rootDir": {"description": "Specifies the root directory of input files. Use to control the output directory structure with --outDir.", "type": "string"}, "isolatedModules": {"description": "Unconditionally emit imports for unresolved files.", "type": "boolean", "default": false}, "sourceMap": {"description": "Generates corresponding '.map' file.", "type": "boolean", "default": false}, "sourceRoot": {"description": "Specifies the location where debugger should locate TypeScript files instead of source locations.", "type": "string"}, "suppressExcessPropertyErrors": {"description": "Suppress excess property checks for object literals. It is recommended to use @ts-ignore comments instead of enabling this setting.", "type": "boolean", "default": false}, "suppressImplicitAnyIndexErrors": {"description": "Suppress noImplicitAny errors for indexing objects lacking index signatures. It is recommended to use @ts-ignore comments instead of enabling this setting.", "type": "boolean", "default": false}, "stripInternal": {"description": "Do not emit declarations for code that has an '@internal' annotation.", "type": "boolean"}, "target": {"description": "Specify ECMAScript target version: 'ES3', 'ES5', 'ES6'/'ES2015', 'ES2016', 'ES2017', 'ES2018', 'ES2019', 'ES2020', 'ESNext'", "type": "string", "default": "ES3", "anyOf": [{"enum": ["ES3", "ES5", "ES6", "ES2015", "ES2016", "ES2017", "ES2018", "ES2019", "ES2020", "ESNext"]}, {"pattern": "^([Ee][Ss]([356]|(20(1[56789]|20))|[Nn][Ee][Xx][Tt]))$"}]}, "watch": {"description": "Watch input files.", "type": "boolean"}, "fallbackPolling": {"description": "Specify the polling strategy to use when the system runs out of or doesn't support native file watchers. Requires TypeScript version 3.8 or later.", "enum": ["fixedPollingInterval", "priorityPollingInterval", "dynamicPriorityPolling"]}, "watchDirectory": {"description": "Specify the strategy for watching directories under systems that lack recursive file-watching functionality. Requires TypeScript version 3.8 or later.", "enum": ["useFsEvents", "fixedPollingInterval", "dynamicPriorityPolling"], "default": "useFsEvents"}, "watchFile": {"description": "Specify the strategy for watching individual files. Requires TypeScript version 3.8 or later.", "enum": ["fixedPollingInterval", "priorityPollingInterval", "dynamicPriorityPolling", "useFsEvents", "useFsEventsOnParentDirectory"], "default": "useFsEvents"}, "experimentalDecorators": {"description": "Enables experimental support for ES7 decorators.", "type": "boolean"}, "emitDecoratorMetadata": {"description": "Emit design-type metadata for decorated declarations in source.", "type": "boolean"}, "allowUnusedLabels": {"description": "Do not report errors on unused labels. Requires TypeScript version 1.8 or later.", "type": "boolean"}, "noImplicitReturns": {"description": "Report error when not all code paths in function return a value. Requires TypeScript version 1.8 or later.", "type": "boolean", "default": false}, "noUncheckedIndexedAccess": {"description": "Add `undefined` to an un-declared field in a type. Requires TypeScript version 4.1 or later.", "type": "boolean"}, "noFallthroughCasesInSwitch": {"description": "Report errors for fallthrough cases in switch statement. Requires TypeScript version 1.8 or later.", "type": "boolean", "default": false}, "allowUnreachableCode": {"description": "Do not report errors on unreachable code. Requires TypeScript version 1.8 or later.", "type": "boolean"}, "forceConsistentCasingInFileNames": {"description": "Disallow inconsistently-cased references to the same file. Enabling this setting is recommended.", "type": "boolean", "default": false}, "generateCpuProfile": {"description": "Emit a v8 CPI profile during the compiler run, which may provide insight into slow builds. Requires TypeScript version 3.7 or later.", "type": "string", "default": "profile.cpuprofile"}, "importNotUsedAsValues": {"description": "This flag controls how imports work. When set to `remove`, imports that only reference types are dropped. When set to `preserve`, imports are never dropped. When set to `error`, imports that can be replaced with `import type` will result in a compiler error. Requires TypeScript version 3.8 or later.", "enum": ["remove", "preserve", "error"]}, "baseUrl": {"description": "Base directory to resolve non-relative module names.", "type": "string"}, "paths": {"description": "Specify path mapping to be computed relative to baseUrl option.", "type": "object", "additionalProperties": {"type": "array", "uniqueItems": true, "items": {"type": "string", "description": "Path mapping to be computed relative to baseUrl option."}}}, "plugins": {"description": "List of TypeScript language server plugins to load. Requires TypeScript version 2.3 or later.", "type": "array", "items": {"type": "object", "properties": {"name": {"description": "Plugin name.", "type": "string"}}}}, "rootDirs": {"description": "Specify list of root directories to be used when resolving modules. Requires TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "typeRoots": {"description": "Specify list of directories for type definition files to be included. Requires TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "types": {"description": "Type declaration files to be included in compilation. Requires TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "traceResolution": {"description": "Enable tracing of the name resolution process. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "allowJs": {"description": "Allow javascript files to be compiled. Requires TypeScript version 1.8 or later.", "type": "boolean", "default": false}, "noErrorTruncation": {"description": "Do not truncate error messages. This setting is deprecated.", "type": "boolean", "default": false}, "allowSyntheticDefaultImports": {"description": "Allow default imports from modules with no default export. This does not affect code emit, just typechecking. Requires TypeScript version 1.8 or later.", "type": "boolean"}, "noImplicitUseStrict": {"description": "Do not emit 'use strict' directives in module output.", "type": "boolean", "default": false}, "listEmittedFiles": {"description": "Enable to list all emitted files. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "disableSizeLimit": {"description": "Disable size limit for JavaScript project. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "lib": {"description": "List of library files to be included in the compilation. Possible values are: 'ES5', 'ES6', 'ES2015', 'ES7', 'ES2016', 'ES2017', 'ES2018', 'ESNext', 'DOM', 'DOM.Iterable', 'WebWorker', 'ScriptHost', 'ES2015.Core', 'ES2015.Collection', 'ES2015.Generator', 'ES2015.Iterable', 'ES2015.Promise', 'ES2015.Proxy', 'ES2015.Reflect', 'ES2015.Symbol', 'ES2015.Symbol.WellKnown', 'ES2016.Array.Include', 'ES2017.object', 'ES2017.Intl', 'ES2017.SharedMemory', 'ES2017.String', 'ES2017.TypedArrays', 'ES2018.Intl', 'ES2018.Promise', 'ES2018.RegExp', 'ESNext.AsyncIterable', 'ESNext.Array', 'ESNext.Intl', 'ESNext.Symbol'. Requires TypeScript version 2.0 or later.", "type": "array", "uniqueItems": true, "items": {"type": "string", "anyOf": [{"enum": ["ES5", "ES6", "ES2015", "ES2015.Collection", "ES2015.Core", "ES2015.Generator", "ES2015.Iterable", "ES2015.Promise", "ES2015.Proxy", "ES2015.Reflect", "ES2015.Symbol.WellKnown", "ES2015.Symbol", "ES2016", "ES2016.Array.Include", "ES2017", "ES2017.Intl", "ES2017.Object", "ES2017.SharedMemory", "ES2017.String", "ES2017.TypedArrays", "ES2018", "ES2018.AsyncGenerator", "ES2018.AsyncIterable", "ES2018.Intl", "ES2018.Promise", "ES2018.Regexp", "ES2019", "ES2019.A<PERSON>y", "ES2019.Object", "ES2019.String", "ES2019.Symbol", "ES2020", "ES2020.BigInt", "ES2020.Promise", "ES2020.String", "ES2020.Symbol.WellKnown", "ESNext", "ESNext.Array", "ESNext.AsyncIterable", "ESNext.BigInt", "ESNext.Intl", "ESNext.Promise", "ESNext.String", "ESNext.Symbol", "DOM", "DOM.Iterable", "ScriptHost", "WebWorker", "WebWorker.ImportScripts"]}, {"pattern": "^[Ee][Ss]5|[Ee][Ss]6|[Ee][Ss]7$"}, {"pattern": "^[Ee][Ss]2015(\\.([Cc][Oo][Ll][Ll][Ee][Cc][Tt][Ii][Oo][Nn]|[Cc][Oo][Rr][Ee]|[Gg][Ee][Nn][Ee][Rr][Aa][Tt][Oo][Rr]|[Ii][Tt][Ee][Rr][Aa][Bb][Ll][Ee]|[Pp][Rr][Oo][Mm][Ii][Ss][Ee]|[Pp][Rr][Oo][Xx][Yy]|[Rr][Ee][Ff][Ll][Ee][Cc][Tt]|[Ss][Yy][Mm][Bb][Oo][Ll].[Ww][Ee][Ll][Ll][Kk][Nn][Oo][Ww][Nn]|[Ss][Yy][Mm][Bb][Oo][Ll]))?$"}, {"pattern": "^[Ee][Ss]2016(\\.[Aa][Rr][Rr][Aa][Yy].[Ii][Nn][Cc][Ll][Uu][Dd][Ee])?$"}, {"pattern": "^[Ee][Ss]2017(\\.([Ii][Nn][Tt][Ll]|[Oo][Bb][Jj][Ee][Cc][Tt]|[Ss][Hh][Aa][Rr][Ee][Dd][Mm][Ee][Mm][Oo][Rr][Yy]|[Ss][Tt][Rr][Ii][Nn][Gg]|[Tt][Yy][Pp][Ee][Dd][Aa][Rr][Rr][Aa][Yy][Ss]))?$"}, {"pattern": "^[Ee][Ss]2018(\\.([Aa][Ss][Yy][Nn][Cc][Ii][Tt][Ee][Rr][Aa][Bb][Ll][Ee]|[Ii][Nn][Tt][Ll]|[Pp][Rr][Oo][Mm][Ii][Ss][Ee]|[Rr][Ee][Gg][Ee][Xx][Pp]))?$"}, {"pattern": "^[Ee][Ss]2019(\\.([Aa][Rr][Rr][Aa][Yy]|[Oo][Bb][Jj][Ee][Cc][Tt]|[Ss][Tt][Rr][Ii][Nn][Gg]|[Ss][Yy][Mm][Bb][Oo][Ll]))?$"}, {"pattern": "^[Ee][Ss]2020(\\.([Bb][Ii][Gg][Ii][Nn][Tt]|[Pp][Rr][Oo][Mm][Ii][Ss][Ee]|[Ss][Tt][Rr][Ii][Nn][Gg]|[Ss][Yy][Mm][Bb][Oo][Ll].[Ww][Ee][Ll][Ll][Kk][Nn][Oo][Ww][Nn]))?$"}, {"pattern": "^[Ee][Ss][Nn][Ee][Xx][Tt](\\.([Aa][Rr][Rr][Aa][Yy]|[Aa][Ss][Yy][Nn][Cc][Ii][Tt][Ee][Rr][Aa][Bb][Ll][Ee]|[Bb][Ii][Gg][Ii][Nn][Tt]|[Ii][Nn][Tt][Ll]|[Ss][Yy][Mm][Bb][Oo][Ll]))?$"}, {"pattern": "^[Dd][Oo][Mm](\\.[Ii][Tt][Ee][Rr][Aa][Bb][Ll][Ee])?$"}, {"pattern": "^[Ss][Cc][Rr][Ii][Pp][Tt][Hh][Oo][Ss][Tt]$"}, {"pattern": "^[Ww][Ee][Bb][Ww][Oo][Rr][Kk][Ee][Rr](\\.[Ii][Mm][Pp][Oo][Rr][Tt][Ss][Cc][Rr][Ii][Pp][Tt][Ss])?$"}]}}, "strictNullChecks": {"description": "Enable strict null checks. Enabling this setting is recommended. Requires TypeScript version 2.0 or later.", "type": "boolean", "default": false}, "maxNodeModuleJsDepth": {"description": "The maximum dependency depth to search under node_modules and load JavaScript files. Only applicable with --allowJs.", "type": "number", "default": 0}, "importHelpers": {"description": "Import emit helpers (e.g. '__extends', '__rest', etc..) from tslib. Requires TypeScript version 2.1 or later.", "type": "boolean", "default": false}, "importsNotUsedAsValues": {"description": "Specify emit/checking behavior for imports that are only used for types. Requires TypeScript version 3.8 or later.", "type": "string", "default": "remove", "enum": ["remove", "preserve", "error"]}, "alwaysStrict": {"description": "Parse in strict mode and emit 'use strict' for each source file. Requires TypeScript version 2.1 or later.", "type": "boolean"}, "strict": {"description": "Enable all strict type checking options. Enabling this setting is recommended. Requires TypeScript version 2.3 or later.", "type": "boolean", "default": false}, "strictBindCallApply": {"description": "Enable stricter checking of of the `bind`, `call`, and `apply` methods on functions. Enabling this setting is recommended. Requires TypeScript version 3.2 or later.", "type": "boolean", "default": false}, "downlevelIteration": {"description": "Provide full support for iterables in 'for-of', spread, and destructuring when targeting 'ES5' or 'ES3'. Requires TypeScript version 2.3 or later.", "type": "boolean", "default": false}, "checkJs": {"description": "Report errors in .js files. Requires TypeScript version 2.3 or later.", "type": "boolean", "default": false}, "strictFunctionTypes": {"description": "Disable bivariant parameter checking for function types. Enabling this setting is recommended. Requires TypeScript version 2.6 or later.", "type": "boolean", "default": false}, "strictPropertyInitialization": {"description": "Ensure non-undefined class properties are initialized in the constructor. Enabling this setting is recommended. Requires TypeScript version 2.7 or later.", "type": "boolean", "default": false}, "esModuleInterop": {"description": "Emit '__importStar' and '__importDefault' helpers for runtime babel ecosystem compatibility and enable '--allowSyntheticDefaultImports' for typesystem compatibility. Enabling this setting is recommended. Requires TypeScript version 2.7 or later.", "type": "boolean", "default": false}, "allowUmdGlobalAccess": {"description": "Allow accessing UMD globals from modules. Requires TypeScript version 3.5 or later.", "type": "boolean", "default": false}, "keyofStringsOnly": {"description": "Resolve 'keyof' to string valued property names only (no numbers or symbols). This setting is deprecated. Requires TypeScript version 2.9 or later.", "type": "boolean", "default": false}, "useDefineForClassFields": {"description": "Emit ECMAScript standard class fields. Requires TypeScript version 3.7 or later.", "type": "boolean", "default": false}, "declarationMap": {"description": "Generates a sourcemap for each corresponding '.d.ts' file. Requires TypeScript version 2.9 or later.", "type": "boolean", "default": false}, "resolveJsonModule": {"description": "Include modules imported with '.json' extension. Requires TypeScript version 2.9 or later.", "type": "boolean", "default": false}, "assumeChangesOnlyAffectDirectDependencies": {"description": "Have recompiles in '--incremental' and '--watch' assume that changes within a file will only affect files directly depending on it. Requires TypeScript version 3.8 or later.", "type": "boolean"}, "extendedDiagnostics": {"description": "Show verbose diagnostic information.", "type": "boolean", "default": false}, "listFilesOnly": {"description": "Print names of files that are part of the compilation and then stop processing.", "type": "boolean"}, "disableSourceOfProjectReferenceRedirect": {"description": "Disable use of source files instead of declaration files from referenced projects. Requires TypeScript version 3.7 or later.", "type": "boolean"}, "disableSolutionSearching": {"description": "Disable solution searching for this project. Requires TypeScript version 3.8 or later.", "type": "boolean"}}}}}, "typeAcquisitionDefinition": {"properties": {"typeAcquisition": {"type": "object", "description": "Auto type (.d.ts) acquisition options for this project. Requires TypeScript version 2.1 or later.", "properties": {"enable": {"description": "Enable auto type acquisition", "type": "boolean", "default": false}, "include": {"description": "Specifies a list of type declarations to be included in auto type acquisition. Ex. [\"jquery\", \"lodash\"]", "type": "array", "uniqueItems": true, "items": {"type": "string"}}, "exclude": {"description": "Specifies a list of type declarations to be excluded from auto type acquisition. Ex. [\"jquery\", \"lodash\"]", "type": "array", "uniqueItems": true, "items": {"type": "string"}}}}}}, "referencesDefinition": {"properties": {"references": {"type": "array", "uniqueItems": true, "description": "Referenced projects. Requires TypeScript version 3.0 or later.", "items": {"type": "object", "description": "Project reference.", "properties": {"path": {"type": "string", "description": "Path to referenced tsconfig or to folder containing tsconfig."}}}}}}, "tsNodeDefinition": {"properties": {"ts-node": {"description": "ts-node options.  See also: https://github.com/TypeStrong/ts-node#configuration-options\n\nts-node offers TypeScript execution and REPL for node.js, with source map support.", "properties": {"compiler": {"default": "typescript", "description": "Specify a custom TypeScript compiler.", "type": "string"}, "compilerHost": {"default": false, "description": "Use TypeScript's compiler host API.", "type": "boolean"}, "compilerOptions": {"additionalProperties": true, "allOf": [{"$ref": "#/definitions/compilerOptionsDefinition/properties/compilerOptions"}], "description": "JSON object to merge with compiler options.", "properties": {}, "type": "object"}, "emit": {"default": false, "description": "Emit output files into `.ts-node` directory.", "type": "boolean"}, "experimentalEsmLoader": {"description": "True if require() hooks should interop with experimental ESM loader.\nEnabled explicitly via a flag since it is a breaking change.", "type": "boolean"}, "files": {"default": false, "description": "Load files from `tsconfig.json` on startup.", "type": "boolean"}, "ignore": {"default": "/node_modules/", "description": "Override the path patterns to skip compilation.", "items": {"type": "string"}, "type": "array"}, "ignoreDiagnostics": {"description": "Ignore TypeScript warnings by diagnostic code.", "items": {"type": ["string", "number"]}, "type": "array"}, "logError": {"default": false, "description": "Logs TypeScript errors to st<PERSON><PERSON> instead of throwing exceptions.", "type": "boolean"}, "preferTsExts": {"default": false, "description": "Re-order file extensions so that TypeScript imports are preferred.", "type": "boolean"}, "pretty": {"default": false, "description": "Use pretty diagnostic formatter.", "type": "boolean"}, "require": {"description": "Modules to require, like node's `--require` flag.\n\nIf specified in tsconfig.json, the modules will be resolved relative to the tsconfig.json file.\n\nIf specified programmatically, each input string should be pre-resolved to an absolute path for\nbest results.", "items": {"type": "string"}, "type": "array"}, "scope": {"default": false, "description": "<PERSON>ope compiler to files within `cwd`.", "type": "boolean"}, "skipIgnore": {"default": false, "description": "<PERSON><PERSON> ignore check.", "type": "boolean"}, "transpileOnly": {"default": false, "description": "Use TypeScript's faster `transpileModule`.", "type": "boolean"}, "typeCheck": {"default": true, "description": "**DEPRECATED** Specify type-check is enabled (e.g. `transpileOnly == false`).", "type": "boolean"}}, "type": "object"}}}}, "type": "object", "allOf": [{"$ref": "#/definitions/compilerOptionsDefinition"}, {"$ref": "#/definitions/compileOnSaveDefinition"}, {"$ref": "#/definitions/typeAcquisitionDefinition"}, {"$ref": "#/definitions/extendsDefinition"}, {"$ref": "#/definitions/tsNodeDefinition"}, {"$ref": "#/definitions/tsNodeDefinition"}, {"anyOf": [{"$ref": "#/definitions/filesDefinition"}, {"$ref": "#/definitions/excludeDefinition"}, {"$ref": "#/definitions/includeDefinition"}, {"$ref": "#/definitions/referencesDefinition"}]}]}